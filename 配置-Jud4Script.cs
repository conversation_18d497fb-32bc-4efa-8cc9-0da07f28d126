using System;
using System.Text;
using System.Windows.Forms;
using Script.Methods;
using Jud4Script;
/************************************
Shell Module default code: using .NET Framwwork 4.6.1
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;

    }

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
        //You can add your codes here, for realizing your desired function
		//每次执行将进入该函数，此处添加所需的逻辑流程处理

        try
        {
            // 将in0参数转换为inspecInfo格式
            string inspecInfo = ConvertIn0ToInspecInfo(in0);

            // 调用判定引擎
            string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);

            // 输出结果
            out0 = result;

            // 记录日志（可选）
            LogMessage($"产品型号: {modelNo}, 检测信息: {inspecInfo}, 判定结果: {result}");
        }
        catch (Exception ex)
        {
            // 异常处理
            out0 = "0"; // 异常时返回0
            LogMessage($"判定过程异常: {ex.Message}");
        }

        return true;
    }

    /// <summary>
    /// 将in0参数转换为Jud4Script需要的inspecInfo格式
    /// </summary>
    /// <param name="in0Value">输入的in0参数值</param>
    /// <returns>转换后的inspecInfo字符串</returns>
    private string ConvertIn0ToInspecInfo(string in0Value)
    {
        if (string.IsNullOrEmpty(in0Value))
        {
            return "CAM=C1;TS=;JD=;;Items=;";
        }

        // 构建inspecInfo字符串
        StringBuilder inspecInfoBuilder = new StringBuilder();

        // 添加固定前缀 - 根据实际相机编号调整
        inspecInfoBuilder.Append("CAM=C1;TS=;JD=;;Items=;");

        try
        {
            // 根据不同的输入格式进行转换
            if (in0Value.Contains("="))
            {
                // 格式1：键值对格式 "项目名1=值1;项目名2=值2"
                ProcessKeyValueFormat(in0Value, inspecInfoBuilder);
            }
            else if (in0Value.Contains(","))
            {
                // 格式2：逗号分隔的数值 "值1,值2,值3"
                ProcessCommaDelimitedFormat(in0Value, inspecInfoBuilder);
            }
            else if (in0Value.Contains(";"))
            {
                // 格式3：分号分隔的数值 "值1;值2;值3"
                ProcessSemicolonDelimitedFormat(in0Value, inspecInfoBuilder);
            }
            else if (IsNumericValue(in0Value))
            {
                // 格式4：单个数值
                inspecInfoBuilder.Append($"检测项目1={in0Value};");
            }
            else
            {
                // 格式5：其他格式，尝试直接添加
                inspecInfoBuilder.Append($"检测项目1={in0Value};");
            }
        }
        catch (Exception ex)
        {
            // 转换异常时记录日志并返回基本格式
            LogMessage($"参数转换异常: {ex.Message}, 输入值: {in0Value}");
            return "CAM=C1;TS=;JD=;;Items=;检测项目1=0.0;";
        }

        return inspecInfoBuilder.ToString();
    }

    /// <summary>
    /// 处理键值对格式：项目名1=值1;项目名2=值2
    /// </summary>
    private void ProcessKeyValueFormat(string input, StringBuilder builder)
    {
        string[] pairs = input.Split(';');
        foreach (string pair in pairs)
        {
            if (!string.IsNullOrWhiteSpace(pair) && pair.Contains("="))
            {
                string cleanPair = pair.Trim();
                // 确保格式正确
                string[] keyValue = cleanPair.Split('=');
                if (keyValue.Length == 2)
                {
                    string key = keyValue[0].Trim();
                    string value = keyValue[1].Trim();

                    // 验证值是否为数值
                    if (IsNumericValue(value))
                    {
                        builder.Append($"{key}={value};");
                    }
                    else
                    {
                        // 非数值时记录警告但仍然添加
                        LogMessage($"警告: 检测项目 {key} 的值 {value} 不是数值");
                        builder.Append($"{key}={value};");
                    }
                }
            }
        }
    }

    /// <summary>
    /// 处理逗号分隔格式：值1,值2,值3
    /// </summary>
    private void ProcessCommaDelimitedFormat(string input, StringBuilder builder)
    {
        string[] values = input.Split(',');
        for (int i = 0; i < values.Length; i++)
        {
            string value = values[i].Trim();
            if (!string.IsNullOrWhiteSpace(value))
            {
                builder.Append($"检测项目{i + 1}={value};");
            }
        }
    }

    /// <summary>
    /// 处理分号分隔格式：值1;值2;值3
    /// </summary>
    private void ProcessSemicolonDelimitedFormat(string input, StringBuilder builder)
    {
        string[] values = input.Split(';');
        int itemIndex = 1;
        foreach (string value in values)
        {
            string cleanValue = value.Trim();
            if (!string.IsNullOrWhiteSpace(cleanValue))
            {
                builder.Append($"检测项目{itemIndex}={cleanValue};");
                itemIndex++;
            }
        }
    }

    /// <summary>
    /// 检查字符串是否为数值
    /// </summary>
    /// <param name="value">要检查的字符串</param>
    /// <returns>是否为数值</returns>
    private bool IsNumericValue(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return false;

        double result;
        return double.TryParse(value, out result);
    }

    /// <summary>
    /// 记录日志信息
    /// </summary>
    /// <param name="message">日志消息</param>
    private void LogMessage(string message)
    {
        try
        {
            string logPath = @"D:\HODD\Jud4Script_Log.txt";
            string logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} - {message}\r\n";

            // 确保目录存在
            string logDir = System.IO.Path.GetDirectoryName(logPath);
            if (!System.IO.Directory.Exists(logDir))
            {
                System.IO.Directory.CreateDirectory(logDir);
            }

            // 写入日志
            System.IO.File.AppendAllText(logPath, logEntry);
        }
        catch
        {
            // 日志记录失败不影响主流程
        }
    }
}
                            