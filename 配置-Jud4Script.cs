using System;
using System.Text;
using System.Windows.Forms;
using Script.Methods;
using Jud4Script;
/************************************
Shell Module default code: using .NET Framwwork 4.6.1
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;

    }

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
        //You can add your codes here, for realizing your desired function
		//每次执行将进入该函数，此处添加所需的逻辑流程处理

        // 简单转换in0为inspecInfo格式
        string inspecInfo = ConvertIn0ToInspecInfo(in0);

        // 调用判定引擎
        string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);

        // 输出结果
        out0 = result;

        return true;
    }

    /// <summary>
    /// 将in0参数转换为Jud4Script需要的inspecInfo格式
    /// 简单转换：C1->CAM1, V->=
    /// </summary>
    /// <param name="in0Value">输入的in0参数值</param>
    /// <returns>转换后的inspecInfo字符串</returns>
    private string ConvertIn0ToInspecInfo(string in0Value)
    {
        if (string.IsNullOrEmpty(in0Value))
        {
            return "CAM=CAM1;TS=;JD=;;Items=;";
        }

        // 构建inspecInfo字符串
        StringBuilder inspecInfoBuilder = new StringBuilder();

        // 固定前缀，C1改成CAM1
        inspecInfoBuilder.Append("CAM=CAM1;TS=;JD=;;Items=;");

        // 简单替换：将V替换为=
        string processedValue = in0Value.Replace("V", "=");

        // 直接添加处理后的值
        if (!processedValue.EndsWith(";"))
        {
            processedValue += ";";
        }

        inspecInfoBuilder.Append(processedValue);

        return inspecInfoBuilder.ToString();
    }



}
                            