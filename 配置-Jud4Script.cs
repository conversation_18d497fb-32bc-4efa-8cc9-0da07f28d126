using System;
using System.Text;
using System.Windows.Forms;
using Script.Methods;
using Jud4Script;
/************************************
Shell Module default code: using .NET Framwwork 4.6.1
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;

    }

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
        //You can add your codes here, for realizing your desired function
		//每次执行将进入该函数，此处添加所需的逻辑流程处理

        // 简单转换in0为inspecInfo格式
        string inspecInfo = ConvertIn0ToInspecInfo(in0);

        // 调用判定引擎
        string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);

        // 输出结果
        out0 = result;

        return true;
    }

    /// <summary>
    /// 将in0参数转换为Jud4Script需要的inspecInfo格式
    /// 根据格式自动识别分隔符，动态识别相机编号
    /// </summary>
    /// <param name="in0Value">输入的in0参数值</param>
    /// <returns>转换后的inspecInfo字符串</returns>
    private string ConvertIn0ToInspecInfo(string in0Value)
    {
        if (string.IsNullOrEmpty(in0Value))
        {
            return "CAM=CAM1;TS=;JD=;;Items=;";
        }

        // 构建inspecInfo字符串
        StringBuilder inspecInfoBuilder = new StringBuilder();

        // 动态识别相机编号
        string cameraId = ExtractCameraId(in0Value);
        inspecInfoBuilder.Append("CAM=" + cameraId + ";TS=;JD=;;Items=;");

        // 根据格式自动识别分隔符并分割
        string[] items;
        if (in0Value.Contains(";"))
        {
            // 使用分号分割
            items = in0Value.Split(';');
        }
        else if (in0Value.Contains(","))
        {
            // 使用逗号分割
            items = in0Value.Split(',');
        }
        else
        {
            // 单个项目
            items = new string[] { in0Value };
        }

        // 处理每个项目
        foreach (string item in items)
        {
            string trimmedItem = item.Trim();
            if (!string.IsNullOrEmpty(trimmedItem))
            {
                // 将V替换为=，同时移除相机编号部分
                string processedItem = ProcessItem(trimmedItem);

                // 添加到结果中
                inspecInfoBuilder.Append(processedItem);

                // 确保以分号结尾
                if (!processedItem.EndsWith(";"))
                {
                    inspecInfoBuilder.Append(";");
                }
            }
        }

        return inspecInfoBuilder.ToString();
    }

    /// <summary>
    /// 从输入字符串中提取相机编号
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>相机编号，如CAM1, CAM2, CAM2A等</returns>
    private string ExtractCameraId(string input)
    {
        if (string.IsNullOrEmpty(input))
        {
            return "CAM1";
        }

        // 查找C1, C2, C2A等模式
        if (input.Contains("C1"))
        {
            return "CAM1";
        }
        else if (input.Contains("C2A"))
        {
            return "CAM2A";
        }
        else if (input.Contains("C2"))
        {
            return "CAM2";
        }
        else if (input.Contains("C3"))
        {
            return "CAM3";
        }
        else if (input.Contains("C4"))
        {
            return "CAM4";
        }
        else if (input.Contains("C5"))
        {
            return "CAM5";
        }

        // 默认返回CAM1
        return "CAM1";
    }

    /// <summary>
    /// 处理单个项目，移除相机编号并替换V为=
    /// </summary>
    /// <param name="item">单个项目字符串</param>
    /// <returns>处理后的项目字符串</returns>
    private string ProcessItem(string item)
    {
        // 移除相机编号（C1, C2, C2A等）
        string processed = item;
        processed = processed.Replace("C1", "");
        processed = processed.Replace("C2A", "");
        processed = processed.Replace("C2", "");
        processed = processed.Replace("C3", "");
        processed = processed.Replace("C4", "");
        processed = processed.Replace("C5", "");

        // 将V替换为=
        processed = processed.Replace("V", "=");

        return processed;
    }



}
                            