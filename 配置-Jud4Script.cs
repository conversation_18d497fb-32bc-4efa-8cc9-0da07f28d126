using System;
using System.Text;
using System.Windows.Forms;
using Script.Methods;
using Jud4Script;
using System.IO;
using System.Collections.Generic;
/************************************
Shell Module default code: using .NET Framwwork 4.6.1
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;

    }

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
        //You can add your codes here, for realizing your desired function
		//每次执行将进入该函数，此处添加所需的逻辑流程处理

        // 将in0参数转换为inspecInfo格式
        string inspecInfo = ConvertIn0ToInspecInfo(in0);

        // 调用判定引擎
        string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);

        // 输出结果
        out0 = result;

        return true;
    }

    /// <summary>
    /// 将in0参数转换为Jud4Script需要的inspecInfo格式
    /// C1代表相机1(CAM1)，外圆碰伤、外圆纹路等为具体缺陷项目名称
    /// </summary>
    /// <param name="in0Value">输入的in0参数值</param>
    /// <returns>转换后的inspecInfo字符串</returns>
    private string ConvertIn0ToInspecInfo(string in0Value)
    {
        if (string.IsNullOrEmpty(in0Value))
        {
            return "CAM=C1;TS=;JD=;;Items=;";
        }

        // 构建inspecInfo字符串
        StringBuilder inspecInfoBuilder = new StringBuilder();

        // 动态获取相机编号
        string cameraId = GetCameraId(modelNo);
        inspecInfoBuilder.Append("CAM=" + cameraId + ";TS=;JD=;;Items=;");

        // 根据不同的输入格式进行转换
        if (in0Value.Contains("="))
        {
            // 格式1：键值对格式 "外圆碰伤=0.000;外圆纹路=0.000"
            ProcessDefectKeyValueFormat(in0Value, inspecInfoBuilder);
        }
        else if (in0Value.Contains(","))
        {
            // 格式2：逗号分隔的数值 "0.000,0.000,0.000"
            ProcessDefectCommaDelimitedFormat(in0Value, inspecInfoBuilder);
        }
        else if (in0Value.Contains(";"))
        {
            // 格式3：分号分隔的数值 "0.000;0.000;0.000"
            ProcessDefectSemicolonDelimitedFormat(in0Value, inspecInfoBuilder);
        }
        else
        {
            // 格式4：单个数值 - 默认为外圆碰伤
            inspecInfoBuilder.Append("外圆碰伤=" + in0Value + ";");
        }

        return inspecInfoBuilder.ToString();
    }

    /// <summary>
    /// 从配置文件或默认规则获取相机编号
    /// </summary>
    /// <param name="modelNo">产品型号</param>
    /// <returns>相机编号，如C1, C5等</returns>
    private string GetCameraId(string modelNo)
    {
        try
        {
            // 构建配置文件路径
            string configPath = Path.Combine(@"D:\LvConfig\产品型号", modelNo + ".json");

            if (File.Exists(configPath))
            {
                // 读取配置文件，查找第一个检测项目的相机编号
                string jsonContent = File.ReadAllText(configPath, Encoding.UTF8);
                string[] lines = jsonContent.Split('\n');

                foreach (string line in lines)
                {
                    string trimmedLine = line.Trim();
                    if (trimmedLine.StartsWith("\"分属相机\"") && trimmedLine.Contains(":"))
                    {
                        int colonIndex = trimmedLine.IndexOf(":");
                        if (colonIndex > 0)
                        {
                            string valuepart = trimmedLine.Substring(colonIndex + 1).Trim();
                            valuepart = valuepart.TrimEnd(',').Trim();
                            if (valuepart.StartsWith("\"") && valuepart.EndsWith("\""))
                            {
                                string cameraName = valuepart.Substring(1, valuepart.Length - 2);
                                // 从相机名称提取编号，如"相机1" -> "C1", "相机5" -> "C5"
                                if (cameraName.Contains("1"))
                                    return "C1";
                                else if (cameraName.Contains("5"))
                                    return "C5";
                                else if (cameraName.Contains("2"))
                                    return "C2";
                                else if (cameraName.Contains("3"))
                                    return "C3";
                                else if (cameraName.Contains("4"))
                                    return "C4";
                            }
                        }
                        break; // 只取第一个相机编号
                    }
                }
            }
        }
        catch
        {
            // 读取失败时使用默认值
        }

        // 默认返回C1
        return "C1";
    }

    /// <summary>
    /// 从配置文件动态读取检测项目名称列表
    /// </summary>
    /// <param name="modelNo">产品型号</param>
    /// <returns>检测项目名称数组</returns>
    private string[] GetDefectItemNames(string modelNo)
    {
        try
        {
            // 构建配置文件路径
            string configPath = Path.Combine(@"D:\LvConfig\产品型号", modelNo + ".json");

            if (!File.Exists(configPath))
            {
                // 配置文件不存在时返回默认项目
                return new string[] { "检测项目1", "检测项目2", "检测项目3" };
            }

            // 读取配置文件内容
            string jsonContent = File.ReadAllText(configPath, Encoding.UTF8);

            // 解析检测项目名称
            List<string> defectNames = new List<string>();

            // 简单的JSON解析，查找"检测项目"字段
            string[] lines = jsonContent.Split('\n');
            foreach (string line in lines)
            {
                string trimmedLine = line.Trim();
                if (trimmedLine.StartsWith("\"检测项目\"") && trimmedLine.Contains(":"))
                {
                    // 提取检测项目名称：查找冒号后的值
                    int colonIndex = trimmedLine.IndexOf(":");
                    if (colonIndex > 0)
                    {
                        string valuepart = trimmedLine.Substring(colonIndex + 1).Trim();
                        // 移除逗号和引号
                        valuepart = valuepart.TrimEnd(',').Trim();
                        if (valuepart.StartsWith("\"") && valuepart.EndsWith("\""))
                        {
                            string itemName = valuepart.Substring(1, valuepart.Length - 2);
                            if (!string.IsNullOrEmpty(itemName))
                            {
                                defectNames.Add(itemName);
                            }
                        }
                    }
                }
            }

            // 如果解析到项目名称，返回；否则返回默认
            if (defectNames.Count > 0)
            {
                return defectNames.ToArray();
            }
            else
            {
                return new string[] { "检测项目1", "检测项目2", "检测项目3" };
            }
        }
        catch
        {
            // 读取失败时返回默认项目
            return new string[] { "检测项目1", "检测项目2", "检测项目3" };
        }
    }

    /// <summary>
    /// 处理缺陷键值对格式：外圆碰伤=0.000;外圆纹路=0.000
    /// </summary>
    private void ProcessDefectKeyValueFormat(string input, StringBuilder builder)
    {
        string[] pairs = input.Split(';');
        foreach (string pair in pairs)
        {
            if (!string.IsNullOrWhiteSpace(pair) && pair.Contains("="))
            {
                string cleanPair = pair.Trim();
                string[] keyValue = cleanPair.Split('=');
                if (keyValue.Length == 2)
                {
                    string defectName = keyValue[0].Trim();
                    string defectValue = keyValue[1].Trim();

                    // 验证缺陷值是否为数值格式
                    builder.Append(defectName + "=" + defectValue + ";");
                }
            }
        }
    }

    /// <summary>
    /// 处理逗号分隔格式：0.000,0.000,0.000 - 按顺序对应缺陷项目
    /// </summary>
    private void ProcessDefectCommaDelimitedFormat(string input, StringBuilder builder)
    {
        string[] values = input.Split(',');
        string[] defectNames = GetDefectItemNames(modelNo);

        for (int i = 0; i < values.Length && i < defectNames.Length; i++)
        {
            string value = values[i].Trim();
            if (!string.IsNullOrWhiteSpace(value))
            {
                builder.Append(defectNames[i] + "=" + value + ";");
            }
        }
    }

    /// <summary>
    /// 处理分号分隔格式：0.000;0.000;0.000 - 按顺序对应缺陷项目
    /// </summary>
    private void ProcessDefectSemicolonDelimitedFormat(string input, StringBuilder builder)
    {
        string[] values = input.Split(';');
        string[] defectNames = GetDefectItemNames(modelNo);
        int itemIndex = 0;

        foreach (string value in values)
        {
            string cleanValue = value.Trim();
            if (!string.IsNullOrWhiteSpace(cleanValue) && itemIndex < defectNames.Length)
            {
                builder.Append(defectNames[itemIndex] + "=" + cleanValue + ";");
                itemIndex++;
            }
        }
    }


}
                            