# Jud4Script 参数转换说明

## 📋 概述

本文档说明如何将VisionMaster中的in0参数转换为Jud4Script.dll需要的inspecInfo格式。

## 🔧 参数格式说明

### 输入参数 (in0)
根据您提供的信息：
- **modelNo (string)**: 产品型号，如 "0523A-F"
- **in0 (string)**: 检测结果数据，格式为数值或检测项目信息

### 输出参数 (inspecInfo)
Jud4Script需要的格式：
```
"CAM=C5;TS=;JD=;;Items=;外圆面伤=180.0;外圆纹路=0.0;槽内多铜=0.0;"
```

## 🎯 转换逻辑

### 基本格式
```csharp
// 固定前缀
"CAM=C1;TS=;JD=;;Items=;"

// 动态检测项目
"检测项目名=数值;"
```

### 转换示例

#### 示例1：单个数值
```csharp
// 输入
in0 = "180.5"
modelNo = "0523A-F"

// 输出
inspecInfo = "CAM=C1;TS=;JD=;;Items=;检测项目1=180.5;"
```

#### 示例2：多个检测项目
```csharp
// 输入
in0 = "外圆面伤=180.0;外圆纹路=0.0;槽内多铜=0.0"
modelNo = "0523A-F"

// 输出
inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆面伤=180.0;外圆纹路=0.0;槽内多铜=0.0;"
```

#### 示例3：数组格式
```csharp
// 输入
in0 = "180.0,0.0,0.0"
modelNo = "0523A-F"

// 输出
inspecInfo = "CAM=C1;TS=;JD=;;Items=;检测项目1=180.0;检测项目2=0.0;检测项目3=0.0;"
```

## 📝 代码实现

### 核心转换函数
```csharp
private string ConvertIn0ToInspecInfo(string in0Value)
{
    if (string.IsNullOrEmpty(in0Value))
    {
        return "CAM=C1;TS=;JD=;;Items=;";
    }
    
    StringBuilder inspecInfoBuilder = new StringBuilder();
    inspecInfoBuilder.Append("CAM=C1;TS=;JD=;;Items=;");
    
    // 根据不同格式进行转换
    if (in0Value.Contains("="))
    {
        // 格式：项目名=值;项目名=值
        ProcessKeyValueFormat(in0Value, inspecInfoBuilder);
    }
    else if (in0Value.Contains(","))
    {
        // 格式：值1,值2,值3
        ProcessCommaDelimitedFormat(in0Value, inspecInfoBuilder);
    }
    else if (IsNumericValue(in0Value))
    {
        // 格式：单个数值
        inspecInfoBuilder.Append($"检测项目1={in0Value};");
    }
    else
    {
        // 其他格式，尝试按分号分割
        ProcessSemicolonDelimitedFormat(in0Value, inspecInfoBuilder);
    }
    
    return inspecInfoBuilder.ToString();
}
```

### 具体处理方法
```csharp
private void ProcessKeyValueFormat(string input, StringBuilder builder)
{
    string[] pairs = input.Split(';');
    foreach (string pair in pairs)
    {
        if (!string.IsNullOrWhiteSpace(pair) && pair.Contains("="))
        {
            builder.Append($"{pair.Trim()};");
        }
    }
}

private void ProcessCommaDelimitedFormat(string input, StringBuilder builder)
{
    string[] values = input.Split(',');
    for (int i = 0; i < values.Length; i++)
    {
        string value = values[i].Trim();
        if (!string.IsNullOrWhiteSpace(value))
        {
            builder.Append($"检测项目{i + 1}={value};");
        }
    }
}
```

## 🔍 配置文件对应

### 配置文件示例 (D:\LvConfig\产品型号\0523A-F.json)
```json
{
  "配置版本": "1.0",
  "型号名称": "0523A-F",
  "检测项目列表": [
    {
      "检测项目": "外圆面伤",
      "分属相机": "相机1",
      "严重分类": "4",
      "轻微分类": "3",
      "良品分类": "1",
      "轻微上限": 200.0,
      "良品上限": 180.0,
      "良品下限": 0.0,
      "轻微下限": 0.0
    }
  ]
}
```

### 检测项目名称匹配
确保in0中的检测项目名称与配置文件中的"检测项目"字段完全匹配：
- ✅ "外圆面伤" - 匹配
- ❌ "外圆面伤1" - 不匹配
- ❌ "外圆面伤 " - 不匹配（有空格）

## 📊 判定结果

### 返回值说明
- **"1"** - 良品分类
- **"2"/"3"/"4"** - 轻微/严重分类（根据配置）
- **"0"** - 异常分类（错误）

### 使用示例
```csharp
public bool Process()
{
    try
    {
        // 转换参数
        string inspecInfo = ConvertIn0ToInspecInfo(in0);
        
        // 调用判定
        string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);
        
        // 输出结果
        out0 = result;
        
        return true;
    }
    catch (Exception ex)
    {
        out0 = "0"; // 异常时返回0
        return false;
    }
}
```

## ⚠️ 注意事项

1. **配置文件路径**: 必须放在 `D:\LvConfig\产品型号\{modelNo}.json`
2. **编码格式**: 配置文件必须使用UTF-8编码
3. **检测项目名称**: 必须与配置文件中的名称完全匹配
4. **数值格式**: 确保数值可以正确解析为double类型
5. **异常处理**: 任何异常都应返回"0"表示异常状态

## 🛠️ 调试建议

1. **启用日志**: 使用LogMessage函数记录转换过程
2. **验证配置**: 确保配置文件格式正确
3. **测试数据**: 使用已知的测试数据验证转换结果
4. **异常捕获**: 捕获并记录所有可能的异常

---

**重要提醒**: 
- 确保Jud4Script.dll和Newtonsoft.Json.dll在同一目录
- 配置文件必须存在且格式正确
- 检测项目名称必须完全匹配
