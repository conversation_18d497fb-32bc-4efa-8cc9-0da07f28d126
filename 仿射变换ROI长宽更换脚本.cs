using System;
using System.Text;
using System.Windows.Forms;
using Script.Methods;
using System.Runtime.InteropServices;
using OpenCvSharp;
using System.IO;
/************************************
Shell Module default code: using .NET Framwwork 4.6.1
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;  

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;
       
    }
    public Mat ImageDataToMat(ImageData img)
	{
	    Mat matImage = new Mat();
	    if(ImagePixelFormate.MONO8 == img.PixelFormat)
	    {
	        matImage = Mat.Zeros(img.Heigth, img.Width, MatType.CV_8UC1);
	        IntPtr grayPtr = Marshal.AllocHGlobal(img.Width * img.Heigth);
	        Marshal.Copy(img.Buffer, 0, matImage.Ptr(0), img.Buffer.Length);
	
	        //用完记得释放指针 
	        Marshal.FreeHGlobal(grayPtr);
	    }
	    else if (ImagePixelFormate.RGB24 == img.PixelFormat)
	    {
	        matImage = Mat.Zeros(img.Heigth, img.Width, MatType.CV_8UC3);
	        IntPtr rgbPtr = Marshal.AllocHGlobal(img.Width * img.Heigth * 3);
	        Marshal.Copy(img.Buffer, 0, matImage.Ptr(0), img.Buffer.Length);
	        Cv2.CvtColor(matImage, matImage, ColorConversionCodes.RGB2BGR);
	
	        //用完记得释放指针 
	        Marshal.FreeHGlobal(rgbPtr);
	    }
	    return matImage;
	}
	public ImageData MatToImageData(Mat matImage)
		{
		    ImageData imgOut = new ImageData();
		    byte[] buffer = new Byte[matImage.Width * matImage.Height * matImage.Channels()];
		    Marshal.Copy(matImage.Ptr(0), buffer, 0, buffer.Length);
		    if (1 == matImage.Channels())
		    {
		        imgOut.Buffer = buffer;
		        imgOut.Width = matImage.Width;
		        imgOut.Heigth = matImage.Height;
		        imgOut.PixelFormat = ImagePixelFormate.MONO8;
		    }
		    else if (3 == matImage.Channels())
		    {
		        //交换R与B通道
		        for (int i = 0; i < buffer.Length - 2; i += 3)
		        {
		            byte temp = buffer[i];
		            buffer[i] = buffer[i + 2];
		            buffer[i + 2] = temp;
		        }
		
		        imgOut.Buffer = buffer;
		        imgOut.Width = matImage.Width;
		        imgOut.Heigth = matImage.Height;
		        imgOut.PixelFormat = ImagePixelFormate.RGB24;
		    }
		    return imgOut;
		}



    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>

    public bool Process()
    {
        //You can add your codes here, for realizing your desired function
		//每次执行将进入该函数，此处添加所需的逻辑流程处理

		        ImageData img    = new ImageData();     //实例化ImageData类型图像. 海康ImageData;Cv.Mat;
				ImageData imgOut = new ImageData();		
				GetImageValue("in0",ref img);      //读取输入图像;		
				Mat matimg = ImageDataToMat(img); 
				Mat Out = ImageDataToMat(imgOut);
				Size newSize = new Size(583, 484);
				Cv2.Resize(matimg, Out, newSize, 0, 0, InterpolationFlags.Linear);
				imgOut = MatToImageData(Out); 
		   		SetImageValue("out0",imgOut);
			//out0 = "1";
        return true;
    }
}
                            