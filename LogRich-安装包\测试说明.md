# LogRich 应用程序测试验证说明

## 测试步骤

### 1. 编译和运行主应用程序
```bash
dotnet build LogRich.csproj
dotnet run
```
应用程序将启动并显示图形界面，包含"监控"和"统计"两个标签页。

### 2. 自动启动Socket监听
- **按需求**：程序启动后自动进入侦听状态，准备接收传入的信息
- 应用程序启动时会自动开始Socket监听端口12380
- 状态将显示"正在监听端口 12380"
- 可以通过"开始监听"/"停止监听"按钮手动控制
- 日志窗口将显示监听状态和批量写入模式提示

### 3. 运行测试客户端
```bash
cd TestClient
dotnet build
dotnet run
```

测试客户端将自动发送以下测试数据：
- `PM:0523A;SN:1001;TS:20250618-093021-233;C1:1;C2:1;C2A:1;C3:3;C4:1;C5:1;C5A:1;`
- `PM:0523A;SN:1002;TS:20250618-093025-155;C1:1;C2:0;C2A:1;C3:1;C4:1;C5:1;C5A:1;`
- `PM:0523A;SN:1003;TS:20250618-093030-677;C1:1;C2:1;C2A:1;C3:1;C4:2;C5:1;C5A:1;`
- `PM:H1213;SN:2001;TS:20250618-093035-123;C1:1;C2:1;C3:1;C4:1;C6:1;`
- `PM:H1213;SN:2002;TS:20250618-093040-456;C1:0;C2:1;C3:1;C4:1;C6:1;`
- `PM:3168P;SN:3001;TS:20250618-093045-789;C1:1;C2:1;C3:1;C4:1;C5:1;C6:1;C7:1;`

### 4. 验证数据接收
在主应用程序的监控标签页中，应该看到：
- 客户端连接日志
- 接收到的数据记录（显示"批量写入模式"）
- 待写入数据数量实时更新
- 每10条数据或5秒后自动写入文件的提示

### 5. 检查CSV文件生成
在`Data`目录下应该生成以下CSV文件：
- `PRec_0523A_20250618.csv`
- `PRec_H1213_20250618.csv`
- `PRec_3168P_20250618.csv`

每个文件包含相应产品型号的检测数据，格式为：
```csv
流水号,检测时间,C1,C2,C2A,C3,C4,C5,C5A,C6,C7
1001,20250618-093021-233,1,1,1,3,1,1,1,,
```

### 6. 测试统计功能
- 切换到"统计"标签页
- 产品型号下拉框会自动加载已有的产品型号
- 选择需要统计的产品型号（如"0523A"）
- 设置日期范围（如2025年6月18日）
- 点击"计算统计"按钮

应该显示：
- 总产量：实际接收的数据条数
- 总体良率：所有相机都为1的产品比例
- 各相机良率表格：每个相机的良品率
- 判定分类统计表格：各分类（0-9）的数量统计

## 验证要点

### ✅ Socket通信功能
- [x] 端口12380监听正常
- [x] 客户端连接成功
- [x] 数据传输无误
- [x] 连接断开处理正常

### ✅ 数据解析功能
- [x] PM（产品型号）解析正确
- [x] SN（流水号）解析正确
- [x] TS（时间戳）解析正确
- [x] 相机结果（C1,C2等）解析正确

### ✅ 文件存储功能
- [x] CSV文件按产品型号和日期分类
- [x] 文件名格式：PRec_{产品型号}_{日期}.csv
- [x] CSV格式正确，包含表头
- [x] 批量写入：每10条数据或5秒超时后写入
- [x] 手动写入：支持"立即写入"按钮
- [x] 待写入数据数量实时显示

### ✅ 统计计算功能
- [x] 总产量计算正确
- [x] **按需求**：当且仅当一个检测信息记录内的各相机判定分类都是1时，才能认定为良品
- [x] 总体良率计算正确（所有相机=1的比例）
- [x] 各相机良率计算正确
- [x] **判定分类统计修正**：分类1（良品）统计的是"整条记录都是1"的记录数量，分类范围0-6（异常、良品、NG1-NG5）

### ✅ 用户界面功能
- [x] 监控标签页显示正常
- [x] 统计标签页显示正常
- [x] 日志显示实时更新
- [x] 数据表格显示正确
- [x] 产品型号自动加载到下拉列表
- [x] 待写入数据数量实时显示
- [x] 立即写入按钮功能正常
- [x] 启动时自动开始监听

## 错误处理验证

1. **端口占用处理**：重复启动应用程序时的错误提示
2. **连接中断处理**：客户端异常断开时的处理
3. **数据格式错误处理**：发送错误格式数据时的处理
4. **文件访问错误处理**：磁盘空间不足或权限问题时的处理

## 性能验证

1. **并发连接**：多个客户端同时连接
2. **大量数据**：连续发送大量检测数据
3. **长时间运行**：应用程序24小时连续运行稳定性

## 故障排除

1. **应用程序无法启动**：检查.NET 6.0是否安装
2. **Socket连接失败**：检查防火墙设置和端口占用
3. **数据解析失败**：检查输入数据格式是否正确
4. **文件无法创建**：检查磁盘空间和文件夹权限 