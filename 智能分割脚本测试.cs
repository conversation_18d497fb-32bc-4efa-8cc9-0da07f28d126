using System;
using System.Text;

/// <summary>
/// 智能分割脚本测试程序
/// 验证根据格式自动识别分隔符的功能
/// </summary>
public class SmartSplitTest
{
    public static void Main(string[] args)
    {
        Console.WriteLine("===========================================");
        Console.WriteLine("    智能分割脚本测试程序");
        Console.WriteLine("===========================================");
        Console.WriteLine();

        var tester = new SmartSplitTest();
        tester.RunAllTests();
        
        Console.WriteLine("===========================================");
        Console.WriteLine("测试完成！按任意键退出...");
        Console.ReadKey();
    }
    
    public void RunAllTests()
    {
        Console.WriteLine("1. 测试分号分割格式");
        TestSemicolonFormat();
        Console.WriteLine();
        
        Console.WriteLine("2. 测试逗号分割格式");
        TestCommaFormat();
        Console.WriteLine();
        
        Console.WriteLine("3. 测试单个项目格式");
        TestSingleFormat();
        Console.WriteLine();
        
        Console.WriteLine("4. 测试混合格式");
        TestMixedFormat();
        Console.WriteLine();
    }
    
    private void TestSemicolonFormat()
    {
        string[] testCases = {
            "外圆碰伤V1.250;外圆纹路V0.500;槽内多铜V0.000",
            "项目AV100.0;项目BV200.0;项目CV300.0",
            "外圆面伤=180.0;外圆纹路=0.0;槽内多铜=0.0"
        };
        
        string[] expectedOutputs = {
            "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;",
            "CAM=CAM1;TS=;JD=;;Items=;项目A=100.0;项目B=200.0;项目C=300.0;",
            "CAM=CAM1;TS=;JD=;;Items=;外圆面伤=180.0;外圆纹路=0.0;槽内多铜=0.0;"
        };
        
        for (int i = 0; i < testCases.Length; i++)
        {
            string result = ConvertIn0ToInspecInfo(testCases[i]);
            Console.WriteLine("  输入: " + testCases[i]);
            Console.WriteLine("  输出: " + result);
            Console.WriteLine("  验证: " + (result == expectedOutputs[i] ? "✅" : "❌"));
            Console.WriteLine();
        }
    }
    
    private void TestCommaFormat()
    {
        string[] testCases = {
            "外圆碰伤V1.250,外圆纹路V0.500,槽内多铜V0.000",
            "项目AV100.0,项目BV200.0",
            "外圆面伤=180.0,外圆纹路=0.0"
        };
        
        string[] expectedOutputs = {
            "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;",
            "CAM=CAM1;TS=;JD=;;Items=;项目A=100.0;项目B=200.0;",
            "CAM=CAM1;TS=;JD=;;Items=;外圆面伤=180.0;外圆纹路=0.0;"
        };
        
        for (int i = 0; i < testCases.Length; i++)
        {
            string result = ConvertIn0ToInspecInfo(testCases[i]);
            Console.WriteLine("  输入: " + testCases[i]);
            Console.WriteLine("  输出: " + result);
            Console.WriteLine("  验证: " + (result == expectedOutputs[i] ? "✅" : "❌"));
            Console.WriteLine();
        }
    }
    
    private void TestSingleFormat()
    {
        string[] testCases = {
            "外圆碰伤V1.250",
            "项目A=100.0",
            "单个数值V500.0"
        };
        
        string[] expectedOutputs = {
            "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;",
            "CAM=CAM1;TS=;JD=;;Items=;项目A=100.0;",
            "CAM=CAM1;TS=;JD=;;Items=;单个数值=500.0;"
        };
        
        for (int i = 0; i < testCases.Length; i++)
        {
            string result = ConvertIn0ToInspecInfo(testCases[i]);
            Console.WriteLine("  输入: " + testCases[i]);
            Console.WriteLine("  输出: " + result);
            Console.WriteLine("  验证: " + (result == expectedOutputs[i] ? "✅" : "❌"));
            Console.WriteLine();
        }
    }
    
    private void TestMixedFormat()
    {
        string[] testCases = {
            "外圆碰伤V1.250;外圆纹路=0.500;槽内多铜V0.000",
            "项目AV100.0,项目B=200.0,项目CV300.0",
            ""
        };
        
        string[] expectedOutputs = {
            "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;",
            "CAM=CAM1;TS=;JD=;;Items=;项目A=100.0;项目B=200.0;项目C=300.0;",
            "CAM=CAM1;TS=;JD=;;Items=;"
        };
        
        for (int i = 0; i < testCases.Length; i++)
        {
            string result = ConvertIn0ToInspecInfo(testCases[i]);
            Console.WriteLine("  输入: \"" + testCases[i] + "\"");
            Console.WriteLine("  输出: " + result);
            Console.WriteLine("  验证: " + (result == expectedOutputs[i] ? "✅" : "❌"));
            Console.WriteLine();
        }
    }
    
    // 复制转换函数用于测试
    private string ConvertIn0ToInspecInfo(string in0Value)
    {
        if (string.IsNullOrEmpty(in0Value))
        {
            return "CAM=CAM1;TS=;JD=;;Items=;";
        }

        StringBuilder inspecInfoBuilder = new StringBuilder();
        inspecInfoBuilder.Append("CAM=CAM1;TS=;JD=;;Items=;");

        // 根据格式自动识别分隔符并分割
        string[] items;
        if (in0Value.Contains(";"))
        {
            items = in0Value.Split(';');
        }
        else if (in0Value.Contains(","))
        {
            items = in0Value.Split(',');
        }
        else
        {
            items = new string[] { in0Value };
        }

        // 处理每个项目
        foreach (string item in items)
        {
            string trimmedItem = item.Trim();
            if (!string.IsNullOrEmpty(trimmedItem))
            {
                // 将V替换为=
                string processedItem = trimmedItem.Replace("V", "=");
                
                // 添加到结果中
                inspecInfoBuilder.Append(processedItem);
                
                // 确保以分号结尾
                if (!processedItem.EndsWith(";"))
                {
                    inspecInfoBuilder.Append(";");
                }
            }
        }

        return inspecInfoBuilder.ToString();
    }
}
