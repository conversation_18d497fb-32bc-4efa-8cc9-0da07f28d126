# VisionMaster 4.3.0 漏检防护系统配置说明

## 🎯 系统概述

本系统专门针对圆形产品钩角检测的漏检问题，通过自动存图、特征匹配和相似性分析来识别潜在的漏检产品。

## 📋 核心功能

### 1. **自动存图功能**
- 每次检测自动保存产品图像
- 按日期分类存储（格式：yyyyMMdd）
- 文件命名：`产品ID_检测结果.bmp`

### 2. **实时漏检监控**
- 基于dis参数进行相似性分析
- 当OK产品与近期NG产品相似度过高时触发警报
- 实时记录检测日志和警报信息

### 3. **离线分析工具**
- 分析历史检测数据
- 生成漏检分析报告
- 支持按时间段筛选分析

## ⚙️ 配置参数

### 关键参数设置

```csharp
// 检测阈值
private const double DIS_THRESHOLD = 385.5; // dis超过此值判定为NG

// 存储路径
private const string SAVE_PATH = @"D:\HODD\ProductImages"; // 图像存储路径
private const string LOG_PATH = @"D:\HODD\DetectionLog.txt"; // 日志路径

// 相似性分析
private const double SIMILARITY_THRESHOLD = 2.0; // 相似度阈值，越小越严格
```

### 参数调整建议

| 参数 | 建议值 | 说明 |
|------|--------|------|
| `DIS_THRESHOLD` | 根据实际产品 | 您提到的缺陷阈值 |
| `SIMILARITY_THRESHOLD` | 1.0-3.0 | 值越小检测越敏感 |
| `SAVE_PATH` | `D:\HODD\ProductImages` | 根据用户偏好设置 |

## 🚀 部署步骤

### 1. VisionMaster脚本集成

将`VisionMaster_检测存图脚本.cs`中的代码集成到您的VisionMaster项目中：

```csharp
// 在VisionMaster脚本中调用
public void OnDetectionComplete()
{
    // 获取检测结果
    string dangleStr = "您的dangle字符串";
    string disStr = "您的dis字符串";
    
    // 执行检测和存图
    var detector = new ProductDetectionScript();
    string result = detector.DetectProduct(dangleStr, disStr);
    
    // 设置外部输出
    out0 = result;
}
```

### 2. 目录结构创建

确保以下目录存在：
```
D:\HODD\
├── ProductImages\          # 产品图像存储
│   ├── 20250711\          # 按日期分类
│   ├── 20250712\
│   └── ...
├── DetectionLog.txt       # 检测日志
├── AlertLog.txt          # 警报日志
└── ErrorLog.txt          # 错误日志
```

### 3. VisionMaster API适配

根据您的VisionMaster版本，调整图像保存API：

```csharp
// 示例：适配VisionMaster 4.3.0
private void SaveImage(string filePath)
{
    // 方法1：使用VisionMaster内置函数
    VisionMaster.SaveCurrentImage(filePath);
    
    // 方法2：使用图像处理模块
    ImageProcessor.SaveImage(filePath);
    
    // 方法3：使用相机模块
    Camera.SaveImage(filePath);
}
```

## 📊 使用流程

### 日常检测流程

1. **启动VisionMaster**，加载包含检测脚本的项目
2. **开始检测**，系统自动：
   - 执行dis参数检测
   - 保存产品图像
   - 记录检测日志
   - 进行实时相似性分析
3. **监控警报**，如出现疑似漏检警报：
   - 检查警报日志
   - 查看相关产品图像
   - 确认是否为真实漏检

### 定期分析流程

1. **运行分析工具**：
   ```bash
   # 编译并运行图像分析工具
   csc 图像分析工具.cs
   图像分析工具.exe
   ```

2. **查看分析报告**：
   - 打开 `D:\HODD\AnalysisResult.txt`
   - 查看疑似漏检产品列表
   - 根据疑似程度优先处理

3. **图像对比验证**：
   - 打开疑似漏检的OK产品图像
   - 对比相似的NG产品图像
   - 人工确认是否为漏检

## 🔍 漏检识别原理

### 相似性计算方法

使用欧几里得距离计算dis参数的相似性：

```csharp
similarity = √(Σ(dis1[i] - dis2[i])²) / n
```

### 判定逻辑

1. **实时监控**：OK产品与1小时内NG产品比较
2. **相似度阈值**：小于设定阈值认为相似
3. **疑似程度分级**：
   - 高度疑似：相似度<0.5 且 dis比值>0.95
   - 中度疑似：相似度<1.0 且 dis比值>0.90
   - 低度疑似：其他情况

## 📈 性能优化

### 内存管理
- 历史记录自动清理（保留24小时）
- 图像按日期分类存储
- 日志文件定期归档

### 处理效率
- 批量图像保存
- 异步日志写入
- 智能相似性计算

## ⚠️ 注意事项

### 1. 存储空间管理
- 每天约产生数GB图像数据
- 建议定期清理旧图像
- 监控磁盘空间使用

### 2. 阈值调整
- 根据实际漏检情况调整`SIMILARITY_THRESHOLD`
- 过小可能产生过多误报
- 过大可能遗漏真实漏检

### 3. 系统性能
- 图像保存会略微影响检测速度
- 建议在非关键路径执行
- 可考虑异步保存

## 🛠️ 故障排除

### 常见问题

1. **图像保存失败**
   - 检查磁盘空间
   - 确认目录权限
   - 验证VisionMaster API调用

2. **相似性分析异常**
   - 检查dis参数格式
   - 确认数组长度一致
   - 验证数值解析正确

3. **日志记录失败**
   - 检查文件路径权限
   - 确认磁盘空间充足
   - 验证文件锁定状态

### 调试建议

1. **启用详细日志**：增加调试信息输出
2. **分步测试**：单独测试各功能模块
3. **参数验证**：确认输入参数格式正确

## 📞 技术支持

如需进一步定制或遇到技术问题，请提供：
1. VisionMaster具体版本信息
2. 产品检测的详细参数
3. 实际的dis和dangle数据样本
4. 系统运行环境信息

---

**注意**：本系统基于您提供的dis参数特征进行漏检识别，dangle参数因位置变化较大暂不用于匹配。如需更精确的识别，可考虑结合图像特征匹配算法。
