using System;
using System.Text;
using System.Windows.Forms;
using System.IO;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;

/************************************
测试数据生成器 - 用于生成测试用的特征值和图像数据
*************************************/
public partial class TestDataGenerator
{
    /// <summary>
    /// 生成测试用的角度特征值
    /// </summary>
    /// <param name="count">特征值数量</param>
    /// <param name="baseAngle">基础角度</param>
    /// <param name="variation">变化范围</param>
    /// <returns>角度特征值字符串</returns>
    public static string GenerateTestAngles(int count = 12, double baseAngle = 0, double variation = 30)
    {
        Random rand = new Random();
        List<string> angles = new List<string>();
        
        for (int i = 0; i < count; i++)
        {
            double angle = baseAngle + (360.0 / count) * i + (rand.NextDouble() - 0.5) * variation;
            angles.Add(angle.ToString("F3"));
        }
        
        return string.Join(";", angles);
    }

    /// <summary>
    /// 生成测试用的距离特征值
    /// </summary>
    /// <param name="count">特征值数量</param>
    /// <param name="baseDistance">基础距离</param>
    /// <param name="variation">变化范围</param>
    /// <returns>距离特征值字符串</returns>
    public static string GenerateTestDistances(int count = 12, double baseDistance = 385, double variation = 5)
    {
        Random rand = new Random();
        List<string> distances = new List<string>();
        
        for (int i = 0; i < count; i++)
        {
            double distance = baseDistance + (rand.NextDouble() - 0.5) * variation;
            distances.Add(distance.ToString("F3"));
        }
        
        return string.Join(";", distances);
    }

    /// <summary>
    /// 生成测试用的图像
    /// </summary>
    /// <param name="width">图像宽度</param>
    /// <param name="height">图像高度</param>
    /// <param name="pattern">图案类型</param>
    /// <returns>图像对象</returns>
    public static Bitmap GenerateTestImage(int width = 1280, int height = 1024, string pattern = "circle")
    {
        Bitmap bitmap = new Bitmap(width, height);
        Graphics g = Graphics.FromImage(bitmap);
        
        // 填充背景
        g.FillRectangle(Brushes.White, 0, 0, width, height);
        
        Random rand = new Random();
        
        switch (pattern.ToLower())
        {
            case "circle":
                // 绘制圆形图案
                int centerX = width / 2;
                int centerY = height / 2;
                int radius = Math.Min(width, height) / 4;
                
                g.DrawEllipse(Pens.Black, centerX - radius, centerY - radius, radius * 2, radius * 2);
                
                // 添加一些随机点
                for (int i = 0; i < 50; i++)
                {
                    int x = rand.Next(width);
                    int y = rand.Next(height);
                    g.FillEllipse(Brushes.Red, x - 2, y - 2, 4, 4);
                }
                break;
                
            case "rectangle":
                // 绘制矩形图案
                int rectWidth = width / 3;
                int rectHeight = height / 3;
                int rectX = (width - rectWidth) / 2;
                int rectY = (height - rectHeight) / 2;
                
                g.DrawRectangle(Pens.Blue, rectX, rectY, rectWidth, rectHeight);
                
                // 添加网格
                for (int i = 0; i < 10; i++)
                {
                    int x = i * width / 10;
                    int y = i * height / 10;
                    g.DrawLine(Pens.Gray, x, 0, x, height);
                    g.DrawLine(Pens.Gray, 0, y, width, y);
                }
                break;
                
            case "random":
            default:
                // 绘制随机图案
                for (int i = 0; i < 100; i++)
                {
                    int x1 = rand.Next(width);
                    int y1 = rand.Next(height);
                    int x2 = rand.Next(width);
                    int y2 = rand.Next(height);
                    
                    Color color = Color.FromArgb(rand.Next(256), rand.Next(256), rand.Next(256));
                    Pen pen = new Pen(color);
                    g.DrawLine(pen, x1, y1, x2, y2);
                }
                break;
        }
        
        g.Dispose();
        return bitmap;
    }

    /// <summary>
    /// 保存测试图像
    /// </summary>
    /// <param name="bitmap">图像对象</param>
    /// <param name="filePath">保存路径</param>
    public static void SaveTestImage(Bitmap bitmap, string filePath)
    {
        try
        {
            string directory = Path.GetDirectoryName(filePath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            
            bitmap.Save(filePath, ImageFormat.Bmp);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"保存测试图像失败: {ex.Message}", "错误", 
                          MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 生成完整的测试数据集
    /// </summary>
    /// <param name="count">测试数据数量</param>
    /// <param name="outputDir">输出目录</param>
    public static void GenerateTestDataSet(int count = 10, string outputDir = @"D:\TestData")
    {
        try
        {
            if (!Directory.Exists(outputDir))
            {
                Directory.CreateDirectory(outputDir);
            }
            
            StringBuilder csvContent = new StringBuilder();
            csvContent.AppendLine("Index,Angles,Distances,ImagePath");
            
            for (int i = 0; i < count; i++)
            {
                // 生成特征值
                string angles = GenerateTestAngles();
                string distances = GenerateTestDistances();
                
                // 生成图像
                string[] patterns = { "circle", "rectangle", "random" };
                string pattern = patterns[i % patterns.Length];
                Bitmap testImage = GenerateTestImage(1280, 1024, pattern);
                
                // 保存图像
                string imagePath = Path.Combine(outputDir, $"test_image_{i:D3}.bmp");
                SaveTestImage(testImage, imagePath);
                
                // 记录到CSV
                csvContent.AppendLine($"{i},{angles},{distances},{imagePath}");
                
                testImage.Dispose();
            }
            
            // 保存CSV文件
            string csvPath = Path.Combine(outputDir, "test_data.csv");
            File.WriteAllText(csvPath, csvContent.ToString());
            
            MessageBox.Show($"成功生成 {count} 个测试数据，保存到: {outputDir}", "完成", 
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"生成测试数据失败: {ex.Message}", "错误", 
                          MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 生成相似的特征值（用于测试匹配功能）
    /// </summary>
    /// <param name="originalAngles">原始角度字符串</param>
    /// <param name="originalDistances">原始距离字符串</param>
    /// <param name="noiseLevel">噪声级别（0-1）</param>
    /// <returns>相似的特征值对</returns>
    public static (string angles, string distances) GenerateSimilarFeatures(
        string originalAngles, string originalDistances, double noiseLevel = 0.1)
    {
        Random rand = new Random();
        
        // 解析原始角度
        string[] angleStrings = originalAngles.Split(';');
        List<string> newAngles = new List<string>();
        
        foreach (string angleStr in angleStrings)
        {
            if (double.TryParse(angleStr, out double angle))
            {
                double noise = (rand.NextDouble() - 0.5) * noiseLevel * 10; // 最大±5度噪声
                double newAngle = angle + noise;
                newAngles.Add(newAngle.ToString("F3"));
            }
        }
        
        // 解析原始距离
        string[] distanceStrings = originalDistances.Split(';');
        List<string> newDistances = new List<string>();
        
        foreach (string distanceStr in distanceStrings)
        {
            if (double.TryParse(distanceStr, out double distance))
            {
                double noise = (rand.NextDouble() - 0.5) * noiseLevel * 20; // 最大±10像素噪声
                double newDistance = distance + noise;
                newDistances.Add(newDistance.ToString("F3"));
            }
        }
        
        return (string.Join(";", newAngles), string.Join(";", newDistances));
    }
}
