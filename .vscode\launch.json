{"version": "0.2.0", "configurations": [{"name": "opencv4.5.5 debuge", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}\\${fileBasenameNoExtension}.exe", "args": [], "stopAtEntry": true, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": true, "MIMode": "gdb", "miDebuggerPath": "D:/mingw64/bin/gdb.exe", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": false}], "preLaunchTask": "opencv4.5.5 compile task"}]}