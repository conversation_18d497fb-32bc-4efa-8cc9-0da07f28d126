# VisionMaster 漏检防护系统测试数据示例

## 📋 数据格式说明

### dangle参数（角度信息）
- 格式：分号分隔的数值字符串
- 单位：度（°）
- 说明：0度到钩中线的角度，因位置变化较大，主要用于记录

### dis参数（距离信息）
- 格式：分号分隔的数值字符串
- 单位：像素或毫米（根据实际标定）
- 说明：钩外径到圆心的距离，用于缺陷检测和相似性分析

## 🧪 测试数据集

### 1. 正常产品数据（OK）

#### 测试样本1
```
dangle: 98.893;128.893;158.892;188.893;218.893;248.893;278.894;308.892;338.894;8.895;38.894;68.894
dis: 385.195;385.079;384.910;385.118;385.148;385.005;385.397;384.984;385.740;386.011;385.533;385.492
预期结果: OK（所有dis值均在阈值385.5以下）
```

#### 测试样本2
```
dangle: 95.123;125.456;155.789;185.234;215.567;245.890;275.123;305.456;335.789;5.234;35.567;65.890
dis: 384.856;384.923;385.012;384.789;385.234;384.567;385.123;384.890;385.345;384.678;385.012;384.934
预期结果: OK（所有dis值均在阈值385.5以下）
```

#### 测试样本3
```
dangle: 102.567;132.890;162.123;192.456;222.789;252.012;282.345;312.678;342.901;12.234;42.567;72.890
dis: 385.234;384.890;385.123;384.567;385.345;384.789;385.012;384.934;385.456;384.678;385.234;384.890
预期结果: OK（所有dis值均在阈值385.5以下）
```

### 2. 缺陷产品数据（NG）

#### 测试样本4
```
dangle: 99.234;129.567;159.890;189.123;219.456;249.789;279.012;309.345;339.678;9.901;39.234;69.567
dis: 385.195;385.079;384.910;385.118;385.148;385.005;385.397;384.984;385.740;386.011;385.533;385.892
预期结果: NG（最后一个dis值385.892超过阈值385.5）
```

#### 测试样本5
```
dangle: 97.890;127.123;157.456;187.789;217.012;247.345;277.678;307.901;337.234;7.567;37.890;67.123
dis: 385.195;385.079;384.910;385.118;385.148;385.005;385.397;384.984;385.740;386.511;385.533;385.492
预期结果: NG（第10个dis值386.511超过阈值385.5）
```

### 3. 疑似漏检测试数据

#### 测试样本6（与样本4相似但被判为OK）
```
dangle: 99.134;129.467;159.790;189.023;219.356;249.689;279.112;309.245;339.578;9.801;39.134;69.467
dis: 385.195;385.079;384.910;385.118;385.148;385.005;385.397;384.984;385.740;386.011;385.533;385.492
预期结果: OK（但与样本4非常相似，应触发疑似漏检警报）
相似度计算: 约0.3（小于阈值2.0，应触发警报）
```

#### 测试样本7（与样本5相似但被判为OK）
```
dangle: 97.790;127.023;157.356;187.689;217.112;247.245;277.578;307.801;337.134;7.467;37.790;67.023
dis: 385.195;385.079;384.910;385.118;385.148;385.005;385.397;384.984;385.740;386.011;385.533;385.492
预期结果: OK（但与样本5相似，应触发疑似漏检警报）
相似度计算: 约0.5（小于阈值2.0，应触发警报）
```

## 🔧 测试步骤

### 1. 基础功能测试
```csharp
// 在VisionMaster中测试基本检测功能
var detector = new ProductDetectionScript();

// 测试OK产品
string result1 = detector.DetectProduct(
    "98.893;128.893;158.892;188.893;218.893;248.893;278.894;308.892;338.894;8.895;38.894;68.894",
    "385.195;385.079;384.910;385.118;385.148;385.005;385.397;384.984;385.740;386.011;385.533;385.492"
);
// 预期: result1 = "OK"

// 测试NG产品
string result2 = detector.DetectProduct(
    "99.234;129.567;159.890;189.123;219.456;249.789;279.012;309.345;339.678;9.901;39.234;69.567",
    "385.195;385.079;384.910;385.118;385.148;385.005;385.397;384.984;385.740;386.011;385.533;385.892"
);
// 预期: result2 = "NG"
```

### 2. 漏检检测测试
```csharp
// 先检测一个NG产品
detector.DetectProduct(
    "99.234;129.567;159.890;189.123;219.456;249.789;279.012;309.345;339.678;9.901;39.234;69.567",
    "385.195;385.079;384.910;385.118;385.148;385.005;385.397;384.984;385.740;386.011;385.533;385.892"
);

// 然后检测相似的OK产品（应触发疑似漏检警报）
detector.DetectProduct(
    "99.134;129.467;159.790;189.023;219.356;249.689;279.112;309.245;339.578;9.801;39.134;69.467",
    "385.195;385.079;384.910;385.118;385.148;385.005;385.397;384.984;385.740;386.011;385.533;385.492"
);
// 预期: 检查AlertLog.txt应有疑似漏检警报
```

### 3. 图像保存测试
```csharp
// 确保图像保存功能正常
// 检查以下目录是否生成图像文件：
// D:\HODD\ProductImages\20250711\产品ID_OK.bmp
// D:\HODD\ProductImages\20250711\产品ID_NG.bmp
```

### 4. 日志记录测试
```csharp
// 检查日志文件是否正常生成：
// D:\HODD\DetectionLog.txt - 检测记录
// D:\HODD\AlertLog.txt - 警报记录
// D:\HODD\ErrorLog.txt - 错误记录
```

## 📊 预期测试结果

### 检测结果统计
- OK产品：3个（样本1、2、3）
- NG产品：2个（样本4、5）
- 疑似漏检：2个（样本6、7）

### 相似性分析结果
- 样本6与样本4相似度：约0.3
- 样本7与样本5相似度：约0.5
- 应触发2次疑似漏检警报

### 文件生成检查
- 图像文件：7个BMP文件
- 日志记录：7条检测记录
- 警报记录：2条疑似漏检警报

## 🎯 测试验证要点

1. **阈值检测准确性**：确保dis超过385.5的产品被正确判为NG
2. **相似性分析有效性**：确保相似产品能触发疑似漏检警报
3. **图像保存完整性**：确保每次检测都保存对应图像
4. **日志记录完整性**：确保所有检测信息都被正确记录
5. **系统稳定性**：连续检测多个产品时系统运行稳定

## 📝 测试报告模板

```
测试时间：____年__月__日 __:__
测试环境：VisionMaster 4.3.0
测试数据：7个样本

检测结果：
- 样本1：OK ✓/✗
- 样本2：OK ✓/✗
- 样本3：OK ✓/✗
- 样本4：NG ✓/✗
- 样本5：NG ✓/✗
- 样本6：OK + 疑似漏检警报 ✓/✗
- 样本7：OK + 疑似漏检警报 ✓/✗

图像保存：✓/✗
日志记录：✓/✗
警报功能：✓/✗

问题记录：
_________________________________
_________________________________

测试结论：通过/不通过
```

---

**注意**：测试时请确保VisionMaster API的图像保存函数已正确配置，并且D:\HODD目录具有写入权限。
