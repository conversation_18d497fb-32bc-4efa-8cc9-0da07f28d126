using System;
using System.IO;
using Jud4Script;

/// <summary>
/// Jud4Script.dll 安装验证测试程序
/// 用于验证安装包是否正确部署和配置
/// </summary>
public class TestJud4Script
{
    public static void Main(string[] args)
    {
        Console.WriteLine("===========================================");
        Console.WriteLine("    Jud4Script.dll 安装验证测试程序");
        Console.WriteLine("===========================================");
        Console.WriteLine();

        // 检查DLL文件是否存在
        Console.WriteLine("1. 检查DLL文件...");
        if (!CheckDllFiles())
        {
            Console.WriteLine("❌ DLL文件检查失败！请确保Jud4Script.dll和Newtonsoft.Json.dll在同一目录。");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
            return;
        }
        Console.WriteLine("✅ DLL文件检查通过");
        Console.WriteLine();

        // 检查配置目录
        Console.WriteLine("2. 检查配置目录...");
        CheckConfigDirectory();
        Console.WriteLine();

        // 创建测试配置文件
        Console.WriteLine("3. 创建测试配置文件...");
        CreateTestConfig();
        Console.WriteLine();

        // 执行功能测试
        Console.WriteLine("4. 执行功能测试...");
        RunFunctionTests();
        Console.WriteLine();

        Console.WriteLine("===========================================");
        Console.WriteLine("测试完成！按任意键退出...");
        Console.ReadKey();
    }

    private static bool CheckDllFiles()
    {
        string currentDir = AppDomain.CurrentDomain.BaseDirectory;
        string jud4ScriptDll = Path.Combine(currentDir, "Jud4Script.dll");
        string newtonsoftDll = Path.Combine(currentDir, "Newtonsoft.Json.dll");

        Console.WriteLine($"  当前目录: {currentDir}");
        Console.WriteLine($"  Jud4Script.dll: {(File.Exists(jud4ScriptDll) ? "存在" : "不存在")}");
        Console.WriteLine($"  Newtonsoft.Json.dll: {(File.Exists(newtonsoftDll) ? "存在" : "不存在")}");

        return File.Exists(jud4ScriptDll) && File.Exists(newtonsoftDll);
    }

    private static void CheckConfigDirectory()
    {
        string configDir = @"D:\LvConfig\产品型号";
        
        Console.WriteLine($"  配置目录: {configDir}");
        
        if (!Directory.Exists(configDir))
        {
            Console.WriteLine("  ⚠️  配置目录不存在，正在创建...");
            try
            {
                Directory.CreateDirectory(configDir);
                Console.WriteLine("  ✅ 配置目录创建成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 配置目录创建失败: {ex.Message}");
            }
        }
        else
        {
            Console.WriteLine("  ✅ 配置目录已存在");
        }
    }

    private static void CreateTestConfig()
    {
        string configPath = @"D:\LvConfig\产品型号\TestModel.json";
        string jsonContent = @"{
  ""配置版本"": ""1.0"",
  ""更新时间"": ""2025-07-11 01:00:00"",
  ""说明"": ""测试配置文件"",
  ""型号名称"": ""TestModel"",
  ""片数"": ""2"",
  ""检测项目列表"": [
    {
      ""检测项目"": ""测试项目1"",
      ""分属相机"": ""测试相机"",
      ""严重分类"": ""4"",
      ""轻微分类"": ""3"",
      ""良品分类"": ""1"",
      ""轻微上限"": 1000.0,
      ""良品上限"": 100.0,
      ""良品下限"": 10.0,
      ""轻微下限"": 5.0,
      ""偏移量"": 0.0,
      ""预处理模式"": """"
    },
    {
      ""检测项目"": ""测试项目2"",
      ""分属相机"": ""测试相机"",
      ""严重分类"": ""4"",
      ""轻微分类"": ""2"",
      ""良品分类"": ""1"",
      ""轻微上限"": 500.0,
      ""良品上限"": 50.0,
      ""良品下限"": 0.0,
      ""轻微下限"": 0.0,
      ""偏移量"": 0.0,
      ""预处理模式"": """"
    }
  ]
}";

        try
        {
            File.WriteAllText(configPath, jsonContent, System.Text.Encoding.UTF8);
            Console.WriteLine($"  ✅ 测试配置文件创建成功: {configPath}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ 测试配置文件创建失败: {ex.Message}");
        }
    }

    private static void RunFunctionTests()
    {
        Console.WriteLine("  执行判定功能测试...");

        // 测试用例1：正常情况 - 良品
        string testCase1 = "CAM=Test;TS=;JD=;;Items=;测试项目1=50.0;测试项目2=25.0;";
        string result1 = JudgmentEngine.Evaluate("TestModel", testCase1);
        Console.WriteLine($"  测试1 - 良品测试: {testCase1}");
        Console.WriteLine($"         结果: {result1} {(result1 == "1" ? "✅" : "❌")}");

        // 测试用例2：轻微缺陷
        string testCase2 = "CAM=Test;TS=;JD=;;Items=;测试项目1=150.0;测试项目2=25.0;";
        string result2 = JudgmentEngine.Evaluate("TestModel", testCase2);
        Console.WriteLine($"  测试2 - 轻微缺陷: {testCase2}");
        Console.WriteLine($"         结果: {result2} {(result2 == "3" ? "✅" : "❌")}");

        // 测试用例3：严重缺陷
        string testCase3 = "CAM=Test;TS=;JD=;;Items=;测试项目1=2000.0;测试项目2=25.0;";
        string result3 = JudgmentEngine.Evaluate("TestModel", testCase3);
        Console.WriteLine($"  测试3 - 严重缺陷: {testCase3}");
        Console.WriteLine($"         结果: {result3} {(result3 == "4" ? "✅" : "❌")}");

        // 测试用例4：异常输入
        string testCase4 = "CAM=Test;TS=;JD=;;Items=;未知项目=50.0;";
        string result4 = JudgmentEngine.Evaluate("TestModel", testCase4);
        Console.WriteLine($"  测试4 - 异常输入: {testCase4}");
        Console.WriteLine($"         结果: {result4} {(result4 == "0" ? "✅" : "❌")}");

        // 测试用例5：多值测试
        string testCase5 = "CAM=Test;TS=;JD=;;Items=;测试项目1*2=50.0,60.0;";
        string result5 = JudgmentEngine.Evaluate("TestModel", testCase5);
        Console.WriteLine($"  测试5 - 多值测试: {testCase5}");
        Console.WriteLine($"         结果: {result5} {(result5 == "1" ? "✅" : "❌")}");

        Console.WriteLine("  功能测试完成");
    }
}
