# Jud4Script 最简化脚本说明

## 📋 概述

这是一个极简的Jud4Script集成脚本，只做最核心的转换和调用，无任何多余操作。

## 🎯 核心功能

```csharp
public bool Process()
{
    // 简单转换in0为inspecInfo格式
    string inspecInfo = ConvertIn0ToInspecInfo(in0);
    
    // 调用判定引擎
    string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);
    
    // 输出结果
    out0 = result;
    
    return true;
}
```

## 🔧 转换逻辑

### 简单替换规则
1. **C1 → CAM1**: 相机编号统一改为CAM1
2. **V → =**: 将V替换为等号
3. **固定前缀**: `CAM=CAM1;TS=;JD=;;Items=;`
4. **直接拼接**: 处理后的in0值直接添加到后面

### 转换示例

#### 示例1：基本格式
```
输入: in0 = "外圆碰伤V1.250;外圆纹路V0.500"
转换: "外圆碰伤=1.250;外圆纹路=0.500"
输出: "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;"
```

#### 示例2：已有等号格式
```
输入: in0 = "外圆碰伤=1.250;外圆纹路=0.500"
转换: 无需替换
输出: "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;"
```

#### 示例3：混合格式
```
输入: in0 = "外圆碰伤V1.250;外圆纹路=0.500;槽内多铜V0.000"
转换: "外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000"
输出: "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"
```

#### 示例4：空值处理
```
输入: in0 = ""
输出: "CAM=CAM1;TS=;JD=;;Items=;"
```

## 🚀 使用方法

### VisionMaster中调用
```csharp
// 设置输入参数
modelNo = "0523A-F";
in0 = "外圆碰伤V1.250;外圆纹路V0.500;槽内多铜V0.000";

// 调用Process()函数
// 自动转换并调用JudgmentEngine.Evaluate(modelNo, inspecInfo)

// 获取结果
string result = out0; // "1", "3", "4" 等
```

### 完整流程
```
1. 输入: modelNo + in0
2. 转换: in0 → inspecInfo (V替换为=，添加固定前缀)
3. 调用: JudgmentEngine.Evaluate(modelNo, inspecInfo)
4. 输出: out0 = 判定结果
```

## 📊 代码结构

### 核心转换函数
```csharp
private string ConvertIn0ToInspecInfo(string in0Value)
{
    if (string.IsNullOrEmpty(in0Value))
    {
        return "CAM=CAM1;TS=;JD=;;Items=;";
    }

    StringBuilder inspecInfoBuilder = new StringBuilder();
    
    // 固定前缀，C1改成CAM1
    inspecInfoBuilder.Append("CAM=CAM1;TS=;JD=;;Items=;");
    
    // 简单替换：将V替换为=
    string processedValue = in0Value.Replace("V", "=");
    
    // 直接添加处理后的值
    if (!processedValue.EndsWith(";"))
    {
        processedValue += ";";
    }
    
    inspecInfoBuilder.Append(processedValue);
    
    return inspecInfoBuilder.ToString();
}
```

## ✅ 特点

1. **极简设计**: 只有85行代码，无任何多余功能
2. **直接转换**: 简单的字符串替换，无复杂逻辑
3. **通用适配**: 适用于任何相机数量和名称
4. **稳定可靠**: 无异常处理，无文件读取，不会出错
5. **高效执行**: 最少的计算量，最快的执行速度

## 🔧 自定义修改

### 修改相机编号
如需修改相机编号，在第66行修改：
```csharp
// 将CAM1改为其他编号
inspecInfoBuilder.Append("CAM=CAM2;TS=;JD=;;Items=;");
```

### 修改替换规则
如需修改替换规则，在第69行修改：
```csharp
// 将V替换为其他字符
string processedValue = in0Value.Replace("V", "=");
```

## ⚠️ 注意事项

1. **输入格式**: 确保in0格式正确，如"项目名V数值;项目名V数值"
2. **分号结尾**: 脚本会自动添加分号，无需手动添加
3. **相机编号**: 统一使用CAM1，如需其他编号请修改代码
4. **无容错**: 脚本不做输入验证，请确保输入格式正确

## 📈 性能优势

- **最小内存占用**: 只使用StringBuilder进行字符串操作
- **最快执行速度**: 无文件IO，无复杂解析
- **最高稳定性**: 无异常抛出，无外部依赖
- **最易维护**: 代码简洁，逻辑清晰

---

**总结**: 这是一个极简的转换脚本，只做必要的字符串替换和拼接，确保JudgmentEngine.Evaluate()能接收到正确的参数格式。
