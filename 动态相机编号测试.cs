using System;
using System.Text;

/// <summary>
/// 动态相机编号识别测试程序
/// 验证根据输入数值动态识别相机编号的功能
/// </summary>
public class DynamicCameraTest
{
    public static void Main(string[] args)
    {
        Console.WriteLine("===========================================");
        Console.WriteLine("    动态相机编号识别测试程序");
        Console.WriteLine("===========================================");
        Console.WriteLine();

        var tester = new DynamicCameraTest();
        tester.RunAllTests();
        
        Console.WriteLine("===========================================");
        Console.WriteLine("测试完成！按任意键退出...");
        Console.ReadKey();
    }
    
    public void RunAllTests()
    {
        Console.WriteLine("1. 测试C1相机编号识别");
        TestC1Camera();
        Console.WriteLine();
        
        Console.WriteLine("2. 测试C2相机编号识别");
        TestC2Camera();
        Console.WriteLine();
        
        Console.WriteLine("3. 测试C2A相机编号识别");
        TestC2ACamera();
        Console.WriteLine();
        
        Console.WriteLine("4. 测试其他相机编号识别");
        TestOtherCameras();
        Console.WriteLine();
        
        Console.WriteLine("5. 测试混合格式");
        TestMixedFormats();
        Console.WriteLine();
    }
    
    private void TestC1Camera()
    {
        string[] testCases = {
            "C1外圆碰伤V1.250;C1外圆纹路V0.500",
            "外圆碰伤C1V1.250,外圆纹路C1V0.500",
            "C1外圆碰伤V1.250"
        };
        
        string[] expectedOutputs = {
            "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;",
            "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;",
            "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;"
        };
        
        for (int i = 0; i < testCases.Length; i++)
        {
            string result = ConvertIn0ToInspecInfo(testCases[i]);
            Console.WriteLine("  输入: " + testCases[i]);
            Console.WriteLine("  输出: " + result);
            Console.WriteLine("  验证: " + (result == expectedOutputs[i] ? "✅" : "❌"));
            Console.WriteLine();
        }
    }
    
    private void TestC2Camera()
    {
        string[] testCases = {
            "C2外圆碰伤V1.250;C2外圆纹路V0.500",
            "外圆碰伤C2V1.250,外圆纹路C2V0.500",
            "C2外圆碰伤V1.250"
        };
        
        string[] expectedOutputs = {
            "CAM=CAM2;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;",
            "CAM=CAM2;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;",
            "CAM=CAM2;TS=;JD=;;Items=;外圆碰伤=1.250;"
        };
        
        for (int i = 0; i < testCases.Length; i++)
        {
            string result = ConvertIn0ToInspecInfo(testCases[i]);
            Console.WriteLine("  输入: " + testCases[i]);
            Console.WriteLine("  输出: " + result);
            Console.WriteLine("  验证: " + (result == expectedOutputs[i] ? "✅" : "❌"));
            Console.WriteLine();
        }
    }
    
    private void TestC2ACamera()
    {
        string[] testCases = {
            "C2A外圆碰伤V1.250;C2A外圆纹路V0.500",
            "外圆碰伤C2AV1.250,外圆纹路C2AV0.500",
            "C2A外圆碰伤V1.250"
        };
        
        string[] expectedOutputs = {
            "CAM=CAM2A;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;",
            "CAM=CAM2A;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;",
            "CAM=CAM2A;TS=;JD=;;Items=;外圆碰伤=1.250;"
        };
        
        for (int i = 0; i < testCases.Length; i++)
        {
            string result = ConvertIn0ToInspecInfo(testCases[i]);
            Console.WriteLine("  输入: " + testCases[i]);
            Console.WriteLine("  输出: " + result);
            Console.WriteLine("  验证: " + (result == expectedOutputs[i] ? "✅" : "❌"));
            Console.WriteLine();
        }
    }
    
    private void TestOtherCameras()
    {
        string[] testCases = {
            "C3外圆碰伤V1.250",
            "C4外圆碰伤V1.250", 
            "C5外圆碰伤V1.250",
            "外圆碰伤V1.250"  // 无相机编号
        };
        
        string[] expectedOutputs = {
            "CAM=CAM3;TS=;JD=;;Items=;外圆碰伤=1.250;",
            "CAM=CAM4;TS=;JD=;;Items=;外圆碰伤=1.250;",
            "CAM=CAM5;TS=;JD=;;Items=;外圆碰伤=1.250;",
            "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;"  // 默认CAM1
        };
        
        for (int i = 0; i < testCases.Length; i++)
        {
            string result = ConvertIn0ToInspecInfo(testCases[i]);
            Console.WriteLine("  输入: " + testCases[i]);
            Console.WriteLine("  输出: " + result);
            Console.WriteLine("  验证: " + (result == expectedOutputs[i] ? "✅" : "❌"));
            Console.WriteLine();
        }
    }
    
    private void TestMixedFormats()
    {
        string[] testCases = {
            "C1外圆碰伤V1.250;外圆纹路=0.500;C1槽内多铜V0.000",
            "C2A外圆碰伤V1.250,C2A外圆纹路V0.500",
            ""
        };
        
        string[] expectedOutputs = {
            "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;",
            "CAM=CAM2A;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;",
            "CAM=CAM1;TS=;JD=;;Items=;"
        };
        
        for (int i = 0; i < testCases.Length; i++)
        {
            string result = ConvertIn0ToInspecInfo(testCases[i]);
            Console.WriteLine("  输入: \"" + testCases[i] + "\"");
            Console.WriteLine("  输出: " + result);
            Console.WriteLine("  验证: " + (result == expectedOutputs[i] ? "✅" : "❌"));
            Console.WriteLine();
        }
    }
    
    // 复制转换函数用于测试
    private string ConvertIn0ToInspecInfo(string in0Value)
    {
        if (string.IsNullOrEmpty(in0Value))
        {
            return "CAM=CAM1;TS=;JD=;;Items=;";
        }

        StringBuilder inspecInfoBuilder = new StringBuilder();
        
        // 动态识别相机编号
        string cameraId = ExtractCameraId(in0Value);
        inspecInfoBuilder.Append("CAM=" + cameraId + ";TS=;JD=;;Items=;");

        // 根据格式自动识别分隔符并分割
        string[] items;
        if (in0Value.Contains(";"))
        {
            items = in0Value.Split(';');
        }
        else if (in0Value.Contains(","))
        {
            items = in0Value.Split(',');
        }
        else
        {
            items = new string[] { in0Value };
        }

        // 处理每个项目
        foreach (string item in items)
        {
            string trimmedItem = item.Trim();
            if (!string.IsNullOrEmpty(trimmedItem))
            {
                // 将V替换为=，同时移除相机编号部分
                string processedItem = ProcessItem(trimmedItem);

                // 添加到结果中
                inspecInfoBuilder.Append(processedItem);

                // 确保以分号结尾
                if (!processedItem.EndsWith(";"))
                {
                    inspecInfoBuilder.Append(";");
                }
            }
        }

        return inspecInfoBuilder.ToString();
    }

    private string ExtractCameraId(string input)
    {
        if (string.IsNullOrEmpty(input))
        {
            return "CAM1";
        }

        // 查找C1, C2, C2A等模式
        if (input.Contains("C1"))
        {
            return "CAM1";
        }
        else if (input.Contains("C2A"))
        {
            return "CAM2A";
        }
        else if (input.Contains("C2"))
        {
            return "CAM2";
        }
        else if (input.Contains("C3"))
        {
            return "CAM3";
        }
        else if (input.Contains("C4"))
        {
            return "CAM4";
        }
        else if (input.Contains("C5"))
        {
            return "CAM5";
        }

        // 默认返回CAM1
        return "CAM1";
    }

    private string ProcessItem(string item)
    {
        // 移除相机编号（C1, C2, C2A等）
        string processed = item;
        processed = processed.Replace("C1", "");
        processed = processed.Replace("C2A", "");
        processed = processed.Replace("C2", "");
        processed = processed.Replace("C3", "");
        processed = processed.Replace("C4", "");
        processed = processed.Replace("C5", "");

        // 将V替换为=
        processed = processed.Replace("V", "=");

        return processed;
    }
}
