using System;
using System.Text;
using System.Windows.Forms;
using System.IO;
using Script.Methods;
using Newtonsoft.Json;
/************************************
Shell Module default code: using .NET Framework 4.6.1
图像丢帧检测模块 - 检测图像采集过程中的丢帧情况

版本信息:
Version: 1.0.0
Date: 2024-01-15
Author: VisionMaster Development Team
Description: 初始版本 - 实现基础丢帧检测功能
Features:
- 支持三个输入参数(fnNew, fnLast, strCn)
- 相机名称解析功能
- fnNew>3时才开始检测，避免初始状态干扰
- JSON格式日志记录(使用Newtonsoft.Json)
- 按日期分文件存储日志
*************************************/
public partial class UserScript : ScriptMethods, IProcessMethods
{
    // 版本常量
    private const string VERSION = "1.0.0";
    private const string BUILD_DATE = "2024-01-15";
    
    //the count of process
    //执行次数计数
    int processCount;

    // 输出参数定义
    string logResult;   // 日志记录结果
    public int fnUpdate { get; set; }

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;
        logResult = "";
    }

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
        try
        {
            // 解析相机名称
            string cameraName = ParseCameraName(strCn);

            // 添加控制条件：仅当fnNew大于3时才开始检查丢帧情况
            if (fnNew <= 3)
            {
                logResult = string.Format("Frame check skipped: fnNew={0} (waiting for fnNew > 3) - Version: {1}", fnNew, VERSION);
                SetStringValue("logResult", logResult);
                processCount++;
                return true;
            }

            // 检测丢帧情况
            bool isFrameLoss = DetectFrameLoss(fnNew, fnLast);

            // 如果存在丢帧，记录日志
            if (isFrameLoss)
            {
                string logEntry = CreateFrameLossLog(cameraName, fnLast, fnNew);
                SaveLogToFile(logEntry);
                logResult = logEntry;
            }
            else
            {
                logResult = string.Format("No frame loss detected - Version: {0}", VERSION);
            }

            // 设置输出参数
            SetStringValue("logResult", logResult);

            fnUpdate = fnNew;
            
            processCount++;
            return true;
        }
                catch (Exception ex)
        {
            // 异常处理
            logResult = string.Format("Error in frame check: {0} - Version: {1}", ex.Message, VERSION);
            SetStringValue("logResult", logResult);
            return false;
        }
    }

    /// <summary>
    /// 解析相机名称
    /// </summary>
    /// <param name="cameraInfo">相机信息字符串</param>
    /// <returns>相机名称</returns>
    private string ParseCameraName(string cameraInfo)
    {
        if (string.IsNullOrEmpty(cameraInfo))
        {
            return "Unknown Camera";
        }

        // 使用分号分割字符串，取第一部分作为相机名称
        string[] parts = cameraInfo.Split(';');
        return parts.Length > 0 ? parts[0].Trim() : "Unknown Camera";
    }

    /// <summary>
    /// 检测是否存在丢帧
    /// </summary>
    /// <param name="currentFrame">当前帧号</param>
    /// <param name="lastFrame">上一帧号</param>
    /// <returns>是否存在丢帧</returns>
    private bool DetectFrameLoss(int currentFrame, int lastFrame)
    {
        // 如果是第一次检测（lastFrame为0），不算丢帧
        if (lastFrame == 0)
        {
            return false;
        }

        // 检查帧号是否连续，如果不连续则表示丢帧
        // 正常情况下，currentFrame应该等于lastFrame + 1
        return (currentFrame != lastFrame + 1);
    }

    /// <summary>
    /// 创建丢帧日志记录
    /// </summary>
    /// <param name="cameraName">相机名称</param>
    /// <param name="lastFrame">上一帧号</param>
    /// <param name="currentFrame">当前帧号</param>
    /// <returns>JSON格式的日志记录</returns>
    private string CreateFrameLossLog(string cameraName, int lastFrame, int currentFrame)
    {
        // 使用Newtonsoft.Json创建日志对象
        var logData = new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"),
            camera_name = cameraName,
            event_type = "frame_loss",
            last_frame = lastFrame,
            current_frame = currentFrame,
            lost_frames = currentFrame - lastFrame - 1,
            description = string.Format("Frame loss detected: expected frame {0}, got frame {1}", lastFrame + 1, currentFrame),
            version = VERSION,
            build_date = BUILD_DATE
        };

        return JsonConvert.SerializeObject(logData, Formatting.Indented);
    }

    /// <summary>
    /// 保存日志到文件
    /// </summary>
    /// <param name="logEntry">日志条目</param>
    private void SaveLogToFile(string logEntry)
    {
        try
        {
            // 创建日志文件路径
            string logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }

            string logFileName = string.Format("FrameLoss_{0}.json", DateTime.Now.ToString("yyyyMMdd"));
            string logFilePath = Path.Combine(logDirectory, logFileName);

            // 追加日志到文件
            using (StreamWriter writer = new StreamWriter(logFilePath, true, Encoding.UTF8))
            {
                writer.WriteLine(logEntry);
                writer.WriteLine(","); // JSON数组分隔符
            }
        }
        catch (Exception ex)
        {
                         // 日志保存失败时的处理
             logResult += string.Format(" (Log save failed: {0})", ex.Message);
        }
    }
}
                            