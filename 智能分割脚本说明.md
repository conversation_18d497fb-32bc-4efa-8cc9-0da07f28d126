# Jud4Script 智能分割脚本说明

## 📋 概述

这是一个智能的Jud4Script集成脚本，能够根据输入格式自动识别分隔符（分号或逗号），处理每个项目后重新组合成正确的格式。

## 🎯 核心功能

```csharp
public bool Process()
{
    // 智能转换in0为inspecInfo格式
    string inspecInfo = ConvertIn0ToInspecInfo(in0);
    
    // 调用判定引擎
    string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);
    
    // 输出结果
    out0 = result;
    
    return true;
}
```

## 🔧 智能分割逻辑

### 自动识别分隔符
1. **优先级1**: 如果包含分号(`;`)，使用分号分割
2. **优先级2**: 如果包含逗号(`,`)，使用逗号分割  
3. **优先级3**: 如果都不包含，作为单个项目处理

### 处理流程
1. **分割**: 根据识别的分隔符分割字符串
2. **处理**: 对每个项目进行V→=替换
3. **组合**: 重新组合成标准格式
4. **输出**: 添加固定前缀和分号结尾

## 📊 转换示例

### 示例1：分号分割格式
```
输入: "外圆碰伤V1.250;外圆纹路V0.500;槽内多铜V0.000"

处理过程:
1. 识别分隔符: 分号(;)
2. 分割: ["外圆碰伤V1.250", "外圆纹路V0.500", "槽内多铜V0.000"]
3. 处理: ["外圆碰伤=1.250", "外圆纹路=0.500", "槽内多铜=0.000"]
4. 组合: "外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"

输出: "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"
```

### 示例2：逗号分割格式
```
输入: "外圆碰伤V1.250,外圆纹路V0.500,槽内多铜V0.000"

处理过程:
1. 识别分隔符: 逗号(,)
2. 分割: ["外圆碰伤V1.250", "外圆纹路V0.500", "槽内多铜V0.000"]
3. 处理: ["外圆碰伤=1.250", "外圆纹路=0.500", "槽内多铜=0.000"]
4. 组合: "外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"

输出: "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"
```

### 示例3：单个项目格式
```
输入: "外圆碰伤V1.250"

处理过程:
1. 识别分隔符: 无分隔符
2. 分割: ["外圆碰伤V1.250"]
3. 处理: ["外圆碰伤=1.250"]
4. 组合: "外圆碰伤=1.250;"

输出: "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;"
```

### 示例4：混合格式
```
输入: "外圆碰伤V1.250;外圆纹路=0.500;槽内多铜V0.000"

处理过程:
1. 识别分隔符: 分号(;)
2. 分割: ["外圆碰伤V1.250", "外圆纹路=0.500", "槽内多铜V0.000"]
3. 处理: ["外圆碰伤=1.250", "外圆纹路=0.500", "槽内多铜=0.000"]
4. 组合: "外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"

输出: "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"
```

### 示例5：空值处理
```
输入: ""
输出: "CAM=CAM1;TS=;JD=;;Items=;"
```

## 🚀 使用方法

### VisionMaster中调用
```csharp
// 设置输入参数 - 支持多种格式
modelNo = "0523A-F";

// 分号格式
in0 = "外圆碰伤V1.250;外圆纹路V0.500;槽内多铜V0.000";

// 或逗号格式
in0 = "外圆碰伤V1.250,外圆纹路V0.500,槽内多铜V0.000";

// 或单个项目
in0 = "外圆碰伤V1.250";

// 或混合格式
in0 = "外圆碰伤V1.250;外圆纹路=0.500;槽内多铜V0.000";

// 调用Process()函数
// 自动识别格式并转换，调用JudgmentEngine.Evaluate(modelNo, inspecInfo)

// 获取结果
string result = out0;
```

## 📈 核心算法

### 智能分割函数
```csharp
private string ConvertIn0ToInspecInfo(string in0Value)
{
    if (string.IsNullOrEmpty(in0Value))
    {
        return "CAM=CAM1;TS=;JD=;;Items=;";
    }

    StringBuilder inspecInfoBuilder = new StringBuilder();
    inspecInfoBuilder.Append("CAM=CAM1;TS=;JD=;;Items=;");

    // 根据格式自动识别分隔符并分割
    string[] items;
    if (in0Value.Contains(";"))
    {
        items = in0Value.Split(';');
    }
    else if (in0Value.Contains(","))
    {
        items = in0Value.Split(',');
    }
    else
    {
        items = new string[] { in0Value };
    }

    // 处理每个项目
    foreach (string item in items)
    {
        string trimmedItem = item.Trim();
        if (!string.IsNullOrEmpty(trimmedItem))
        {
            // 将V替换为=
            string processedItem = trimmedItem.Replace("V", "=");
            
            // 添加到结果中
            inspecInfoBuilder.Append(processedItem);
            
            // 确保以分号结尾
            if (!processedItem.EndsWith(";"))
            {
                inspecInfoBuilder.Append(";");
            }
        }
    }

    return inspecInfoBuilder.ToString();
}
```

## ✅ 特点优势

1. **智能识别**: 自动识别分号或逗号分隔符
2. **格式兼容**: 支持多种输入格式
3. **自动处理**: V→=替换，分号结尾自动添加
4. **容错性强**: 空值、空项目自动过滤
5. **高效简洁**: 只有112行代码，逻辑清晰

## 🔧 支持的格式

| 输入格式 | 分隔符 | 示例 |
|---------|--------|------|
| 分号分割 | `;` | `"项目AV1.0;项目BV2.0"` |
| 逗号分割 | `,` | `"项目AV1.0,项目BV2.0"` |
| 单个项目 | 无 | `"项目AV1.0"` |
| 混合格式 | `;` | `"项目AV1.0;项目B=2.0"` |
| 空值 | 无 | `""` |

## ⚠️ 注意事项

1. **分隔符优先级**: 分号优先于逗号
2. **V替换**: 所有V都会被替换为=
3. **空项目过滤**: 空白项目会被自动过滤
4. **分号结尾**: 每个项目都会以分号结尾
5. **固定前缀**: 统一使用CAM=CAM1

## 🎯 适用场景

- ✅ **多种数据源**: 支持不同系统的输出格式
- ✅ **格式统一**: 自动转换为标准格式
- ✅ **兼容性强**: 向前兼容旧格式
- ✅ **维护简单**: 无需修改代码适配新格式

---

**总结**: 这是一个智能的格式转换脚本，能够自动识别输入格式并正确转换，确保JudgmentEngine.Evaluate()接收到正确的参数。
