# Jud4Script 配置文件说明

## 📋 配置概述

Jud4Script使用JSON格式的配置文件来定义每个产品型号的检测项参数。配置文件包含判定范围、偏移量、分类信息等关键参数。

## 📁 配置文件位置

### 主配置路径
```
D:\LvConfig\产品型号\{产品型号}.json
```

### 备用配置路径
```
当前程序目录\{产品型号}.json
```

**说明**：系统会优先查找主配置路径，如果不存在则查找备用路径。

## 📄 配置文件格式

### 基本结构
```json
{
  "检测项名称1": {
    "Camera": "相机编号",
    "GoodLower": 良品下限,
    "GoodUpper": 良品上限,
    "LightLower": 轻微下限,
    "LightUpper": 轻微上限,
    "Offset": 偏移量,
    "GoodClass": "良品分类",
    "LightClass": "轻微分类",
    "SevereClass": "严重分类",
    "预处理模式": "预留字段"
  },
  "检测项名称2": {
    // 同上结构
  }
}
```

### 示例配置文件 (0523A-F.json)
```json
{
  "槽深": {
    "Camera": "相机5",
    "GoodLower": 0.6,
    "GoodUpper": 0.8,
    "LightLower": 0.5,
    "LightUpper": 0.6,
    "Offset": 0.0,
    "GoodClass": "1",
    "LightClass": "5",
    "SevereClass": "9",
    "预处理模式": ""
  },
  "槽宽": {
    "Camera": "相机5",
    "GoodLower": 0.3,
    "GoodUpper": 0.5,
    "LightLower": 0.25,
    "LightUpper": 0.3,
    "Offset": 0.0,
    "GoodClass": "1",
    "LightClass": "5",
    "SevereClass": "9",
    "预处理模式": ""
  },
  "槽槽夹角": {
    "Camera": "相机5",
    "GoodLower": -180.0,
    "GoodUpper": 180.0,
    "LightLower": -200.0,
    "LightUpper": 200.0,
    "Offset": 0.0,
    "GoodClass": "1",
    "LightClass": "5",
    "SevereClass": "9",
    "预处理模式": ""
  }
}
```

## 🔧 参数详解

### 1. Camera (相机编号)
- **类型**：字符串
- **说明**：检测项对应的相机编号
- **示例**：`"相机5"`、`"CAM1"`、`"C5"`
- **用途**：标识检测项来源，便于追溯和管理

### 2. GoodLower / GoodUpper (良品范围)
- **类型**：浮点数
- **说明**：良品判定的下限和上限
- **示例**：`"GoodLower": 0.6, "GoodUpper": 0.8`
- **判定**：测量值在此范围内判定为 **OK**

### 3. LightLower / LightUpper (轻微范围)
- **类型**：浮点数
- **说明**：轻微不良判定的下限和上限
- **示例**：`"LightLower": 0.5, "LightUpper": 0.6`
- **判定**：测量值在此范围内（且不在良品范围）判定为 **NG1**

### 4. Offset (偏移量)
- **类型**：浮点数
- **说明**：测量值校正偏移量
- **示例**：`"Offset": 0.0`、`"Offset": -0.1`
- **计算**：`调整值 = 测量值 + 偏移量`

### 5. GoodClass (良品分类)
- **类型**：字符串
- **取值范围**：`"1"` - `"9"`
- **说明**：良品情况下的出料分类
- **示例**：`"GoodClass": "1"`

### 6. LightClass (轻微分类)
- **类型**：字符串
- **取值范围**：`"1"` - `"9"`
- **说明**：轻微不良情况下的出料分类
- **示例**：`"LightClass": "5"`

### 7. SevereClass (严重分类)
- **类型**：字符串
- **取值范围**：`"1"` - `"9"`
- **说明**：严重不良情况下的出料分类
- **示例**：`"SevereClass": "9"`

### 8. 预处理模式 (预留字段)
- **类型**：字符串
- **说明**：为未来功能扩展预留的字段
- **示例**：`"预处理模式": ""`
- **当前**：暂未使用，建议设为空字符串

## 📊 判定逻辑

### 单个测量值判定
```
调整值 = 测量值 + Offset

if (调整值 >= GoodLower && 调整值 <= GoodUpper)
    return "OK"
else if (调整值 >= LightLower && 调整值 <= LightUpper)
    return "NG1"
else
    return "NG2"
```

### 多个测量值判定
- 对每个测量值分别进行判定
- 选择最严重的判定结果作为最终结果
- 严重程度：**ERROR** > **NG2** > **NG1** > **OK**

### 最终分类确定
```
if (所有检测项都是 OK)
    return GoodClass
else if (有检测项是 NG1，无 NG2)
    return LightClass
else if (有检测项是 NG2)
    return SevereClass
else if (有检测项是 ERROR)
    return "0"
```

## 🔍 配置验证

### 自动验证规则
系统会自动验证配置文件的有效性：

1. **范围验证**：
   - `GoodLower <= GoodUpper`
   - `LightLower <= LightUpper`

2. **分类验证**：
   - `GoodClass`、`LightClass`、`SevereClass` 必须在 `"1"` - `"9"` 范围内

3. **数据类型验证**：
   - 数值字段必须为有效数字
   - 字符串字段不能为null

### 配置错误示例
```json
{
  "错误示例": {
    "Camera": "相机5",
    "GoodLower": 0.8,      // 错误：下限大于上限
    "GoodUpper": 0.6,
    "LightLower": 0.5,
    "LightUpper": 0.6,
    "Offset": 0.0,
    "GoodClass": "10",     // 错误：超出范围
    "LightClass": "5",
    "SevereClass": "9",
    "预处理模式": ""
  }
}
```

## 🛠️ 配置文件管理

### 创建新配置
1. 复制示例配置文件 `0523A-F.json`
2. 重命名为新的产品型号，如 `NEW-MODEL.json`
3. 修改检测项名称和参数
4. 放置到正确的配置目录

### 修改现有配置
1. 备份原配置文件
2. 使用文本编辑器修改JSON文件
3. 验证JSON格式正确性
4. 测试新配置是否正常工作

### 配置文件模板
```json
{
  "检测项1": {
    "Camera": "相机编号",
    "GoodLower": 下限值,
    "GoodUpper": 上限值,
    "LightLower": 下限值,
    "LightUpper": 上限值,
    "Offset": 偏移值,
    "GoodClass": "1",
    "LightClass": "5",
    "SevereClass": "9",
    "预处理模式": ""
  }
}
```

## 💡 配置最佳实践

### 1. 范围设置建议
- **良品范围**：设置为理想的产品质量范围
- **轻微范围**：设置为可接受的质量偏差范围
- **严重范围**：超出轻微范围的都视为严重不良

### 2. 分类设置建议
- **良品分类**：通常设为 `"1"`（第1出料口）
- **轻微分类**：设为中间值，如 `"5"`
- **严重分类**：设为最大值，如 `"9"`

### 3. 偏移量使用
- 用于校正系统性偏差
- 正值：测量值偏小时使用
- 负值：测量值偏大时使用
- 零值：无需校正时使用

### 4. 文件管理
- 定期备份配置文件
- 使用版本控制管理配置变更
- 建立配置文件命名规范
- 记录配置修改历史

## 🚨 常见问题

### 1. 配置文件不存在
**错误信息**：`配置文件不存在：D:\LvConfig\产品型号\XXX.json`

**解决方法**：
- 检查产品型号是否正确
- 确认配置文件路径和文件名
- 创建对应的配置文件

### 2. JSON格式错误
**错误信息**：`配置文件格式错误`

**解决方法**：
- 使用JSON验证工具检查格式
- 检查括号、引号、逗号是否匹配
- 确保数值不包含非法字符

### 3. 配置参数无效
**错误信息**：`配置验证失败`

**解决方法**：
- 检查数值范围是否合理
- 确认分类字段在 `"1"` - `"9"` 范围内
- 验证下限不大于上限

## 📞 技术支持

配置相关问题请：
1. 检查配置文件格式和路径
2. 使用调试模式查看详细错误信息
3. 参考示例配置文件
4. 联系开发团队获取支持
