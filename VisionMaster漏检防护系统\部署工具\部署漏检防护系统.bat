@echo off
chcp 65001 >nul
echo ========================================
echo VisionMaster 漏检防护系统部署工具
echo ========================================
echo.

echo 正在创建必要的目录结构...
echo.

:: 创建主目录
if not exist "D:\HODD" (
    mkdir "D:\HODD"
    echo ✓ 创建主目录: D:\HODD
) else (
    echo ✓ 主目录已存在: D:\HODD
)

:: 创建图像存储目录
if not exist "D:\HODD\ProductImages" (
    mkdir "D:\HODD\ProductImages"
    echo ✓ 创建图像目录: D:\HODD\ProductImages
) else (
    echo ✓ 图像目录已存在: D:\HODD\ProductImages
)

:: 创建今日图像目录
set TODAY=%date:~0,4%%date:~5,2%%date:~8,2%
if not exist "D:\HODD\ProductImages\%TODAY%" (
    mkdir "D:\HODD\ProductImages\%TODAY%"
    echo ✓ 创建今日目录: D:\HODD\ProductImages\%TODAY%
) else (
    echo ✓ 今日目录已存在: D:\HODD\ProductImages\%TODAY%
)

echo.
echo 正在初始化日志文件...
echo.

:: 创建检测日志文件头
if not exist "D:\HODD\DetectionLog.txt" (
    echo 检测时间,产品ID,检测结果,最大dis,dis参数列表,图像路径 > "D:\HODD\DetectionLog.txt"
    echo ✓ 创建检测日志: D:\HODD\DetectionLog.txt
) else (
    echo ✓ 检测日志已存在: D:\HODD\DetectionLog.txt
)

:: 创建警报日志
if not exist "D:\HODD\AlertLog.txt" (
    echo. > "D:\HODD\AlertLog.txt"
    echo ✓ 创建警报日志: D:\HODD\AlertLog.txt
) else (
    echo ✓ 警报日志已存在: D:\HODD\AlertLog.txt
)

:: 创建错误日志
if not exist "D:\HODD\ErrorLog.txt" (
    echo. > "D:\HODD\ErrorLog.txt"
    echo ✓ 创建错误日志: D:\HODD\ErrorLog.txt
) else (
    echo ✓ 错误日志已存在: D:\HODD\ErrorLog.txt
)

echo.
echo 正在编译分析工具...
echo.

:: 检查是否有.NET编译器
where csc >nul 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  警告: 未找到C#编译器，请确保已安装.NET Framework或.NET SDK
    echo    可以手动编译图像分析工具.cs文件
) else (
    :: 编译图像分析工具
    csc "..\核心代码\图像分析工具.cs" /out:"D:\HODD\ImageAnalyzer.exe"
    if %errorlevel% equ 0 (
        echo ✓ 编译成功: D:\HODD\ImageAnalyzer.exe
    ) else (
        echo ❌ 编译失败，请检查代码文件
    )
)

echo.
echo 正在创建配置文件...
echo.

:: 创建配置文件
(
echo # VisionMaster 漏检防护系统配置
echo # 请根据实际情况调整以下参数
echo.
echo [检测参数]
echo DIS_THRESHOLD=385.5
echo SIMILARITY_THRESHOLD=2.0
echo.
echo [存储路径]
echo SAVE_PATH=D:\HODD\ProductImages
echo LOG_PATH=D:\HODD\DetectionLog.txt
echo.
echo [系统设置]
echo AUTO_CLEANUP_HOURS=24
echo ALERT_WINDOW_MINUTES=60
echo.
echo [图像格式]
echo IMAGE_FORMAT=BMP
echo COMPRESSION_QUALITY=100
) > "D:\HODD\Config.txt"

echo ✓ 创建配置文件: D:\HODD\Config.txt

echo.
echo 正在创建快速启动脚本...
echo.

:: 创建分析工具启动脚本
(
echo @echo off
echo chcp 65001 ^>nul
echo echo 启动图像分析工具...
echo cd /d "D:\HODD"
echo if exist "ImageAnalyzer.exe" ^(
echo     ImageAnalyzer.exe
echo ^) else ^(
echo     echo 请先运行部署脚本编译分析工具
echo     pause
echo ^)
) > "D:\HODD\启动分析工具.bat"

echo ✓ 创建启动脚本: D:\HODD\启动分析工具.bat

:: 创建日志查看脚本
(
echo @echo off
echo chcp 65001 ^>nul
echo echo ========================================
echo echo 漏检防护系统日志查看器
echo echo ========================================
echo echo.
echo echo 1. 查看检测日志
echo echo 2. 查看警报日志  
echo echo 3. 查看错误日志
echo echo 4. 查看分析报告
echo echo.
echo set /p choice=请选择要查看的日志 ^(1-4^): 
echo.
echo if "%%choice%%"=="1" type "D:\HODD\DetectionLog.txt"
echo if "%%choice%%"=="2" type "D:\HODD\AlertLog.txt"
echo if "%%choice%%"=="3" type "D:\HODD\ErrorLog.txt"
echo if "%%choice%%"=="4" type "D:\HODD\AnalysisResult.txt"
echo.
echo pause
) > "D:\HODD\查看日志.bat"

echo ✓ 创建日志查看器: D:\HODD\查看日志.bat

:: 创建清理脚本
(
echo @echo off
echo chcp 65001 ^>nul
echo echo ========================================
echo echo 漏检防护系统清理工具
echo echo ========================================
echo echo.
echo echo 警告: 此操作将删除历史数据，请确认！
echo echo.
echo echo 1. 清理7天前的图像
echo echo 2. 清理所有图像
echo echo 3. 清理日志文件
echo echo 4. 取消操作
echo echo.
echo set /p choice=请选择清理选项 ^(1-4^): 
echo.
echo if "%%choice%%"=="1" ^(
echo     echo 清理7天前的图像...
echo     forfiles /p "D:\HODD\ProductImages" /m *.* /d -7 /c "cmd /c del @path" 2^>nul
echo     echo 清理完成
echo ^)
echo if "%%choice%%"=="2" ^(
echo     echo 确认删除所有图像？ ^(Y/N^)
echo     set /p confirm=
echo     if /i "%%confirm%%"=="Y" ^(
echo         rd /s /q "D:\HODD\ProductImages"
echo         mkdir "D:\HODD\ProductImages"
echo         echo 所有图像已清理
echo     ^)
echo ^)
echo if "%%choice%%"=="3" ^(
echo     echo 确认清理日志文件？ ^(Y/N^)
echo     set /p confirm=
echo     if /i "%%confirm%%"=="Y" ^(
echo         echo 检测时间,产品ID,检测结果,最大dis,dis参数列表,图像路径 ^> "D:\HODD\DetectionLog.txt"
echo         echo. ^> "D:\HODD\AlertLog.txt"
echo         echo. ^> "D:\HODD\ErrorLog.txt"
echo         echo 日志文件已清理
echo     ^)
echo ^)
echo.
echo pause
) > "D:\HODD\系统清理.bat"

echo ✓ 创建清理工具: D:\HODD\系统清理.bat

echo.
echo ========================================
echo 部署完成！
echo ========================================
echo.
echo 已创建的文件和目录：
echo ✓ D:\HODD\ProductImages\          - 图像存储目录
echo ✓ D:\HODD\DetectionLog.txt        - 检测日志
echo ✓ D:\HODD\AlertLog.txt            - 警报日志
echo ✓ D:\HODD\ErrorLog.txt            - 错误日志
echo ✓ D:\HODD\Config.txt              - 配置文件
echo ✓ D:\HODD\ImageAnalyzer.exe       - 分析工具
echo ✓ D:\HODD\启动分析工具.bat        - 快速启动
echo ✓ D:\HODD\查看日志.bat            - 日志查看器
echo ✓ D:\HODD\系统清理.bat            - 清理工具
echo.
echo 下一步操作：
echo 1. 将VisionMaster_检测存图脚本.cs集成到您的VisionMaster项目
echo 2. 根据实际情况调整D:\HODD\Config.txt中的参数
echo 3. 测试图像保存功能
echo 4. 运行分析工具验证系统工作正常
echo.
echo 详细配置说明请参考：配置文档\VisionMaster漏检防护配置说明.md
echo.
pause
