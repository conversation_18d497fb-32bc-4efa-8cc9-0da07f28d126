配置文件示例说明
==================

本目录包含了几个产品型号的配置文件示例，供参考使用。

文件列表：
---------
1. Sample00.json     - 标准三项检测配置示例
2. 0523A-F.json      - 槽类检测配置示例
3. C5.json           - 综合检测配置示例

配置文件格式：
-----------
配置文件采用新的JSON对象格式，包含以下主要字段：
- "配置版本": 配置文件版本号
- "更新时间": 配置文件更新时间
- "说明": 配置文件说明
- "型号名称": 产品型号名称
- "片数": 产品片数
- "检测项目列表": 包含所有检测项配置的数组

检测项目配置字段：
- "检测项目": 检测项名称
- "分属相机": 相机标识
- "严重分类": NG2级别出料分类
- "轻微分类": NG1级别出料分类
- "良品分类": OK级别出料分类
- "轻微上限": 轻微缺陷上限值
- "良品上限": 良品范围上限值
- "良品下限": 良品范围下限值
- "轻微下限": 轻微缺陷下限值
- "偏移量": 测量值校正偏移量
- "预处理模式": 预留字段

使用方法：
---------
1. 选择合适的配置文件作为模板
2. 根据实际产品要求修改检测项目和数值范围
3. 将配置文件复制到：D:\LvConfig\产品型号\{产品型号}.json
4. 确保文件使用UTF-8编码保存

重要提醒：
---------
- 配置文件必须放在固定路径：D:\LvConfig\产品型号\
- 文件名必须与产品型号参数完全一致
- 必须使用UTF-8编码保存，避免中文乱码
- 数值约束必须满足：良品上限≥良品下限，轻微上限≥良品上限，轻微下限≤良品下限
- 分类代码必须在'1'-'9'范围内且不重复
- 检测项目配置必须在"检测项目列表"字段中

详细说明请参考docs目录中的完整文档。
