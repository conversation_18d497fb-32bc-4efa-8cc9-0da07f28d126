// using System;
// using System.Xml;
// using System.IO;

// // 移除了不必要的namespace
// public class Program
// {
//     // 移除了未使用的processCount字段
//     string xmlFilePath = string.Empty;
//     public string CurrentParamName = string.Empty;
//     public float CurrentUpperLimit;

//     public void Init()
//     {
//         xmlFilePath = @"E:\test\test1.xml";
//     }

//     public bool Process()
//     {
//         try
//         {
//             if (!File.Exists(xmlFilePath))
//             {
//                 Console.WriteLine("错误：XML文件不存在！路径: " + xmlFilePath);
//                 return false;
//             }

//             XmlDocument xmlDoc = new();
//             xmlDoc.Load(xmlFilePath);

//             XmlNodeList? nodes = xmlDoc.SelectNodes("/Parameters/Parameter");
//             if (nodes == null || nodes.Count == 0)
//             {
//                 Console.WriteLine("警告：未找到Parameter节点");
//                 return false;
//             }

//             foreach (XmlNode node in nodes)
//             {
//                 string? name = node.SelectSingleNode("Name")?.InnerText;
//                 string? upperText = node.SelectSingleNode("UpperLimit")?.InnerText;
//                 string? lowerText = node.SelectSingleNode("LowerLimit")?.InnerText;

//                 if (string.IsNullOrEmpty(name) || 
//                     !float.TryParse(upperText, out float upper) || 
//                     !float.TryParse(lowerText, out float lower))
//                 {
//                     Console.WriteLine("跳过无效节点数据");
//                     continue;
//                 }

//                 CurrentParamName = name;
//                 CurrentUpperLimit = upper;
//                 Console.WriteLine($"参数: {name}, 范围: {lower}~{upper}");
//             }
//             return true;
//         }
//         catch (Exception ex)
//         {
//             Console.WriteLine("致命错误: " + ex.Message);
//             return false;
//         }
//     }

//     public static void Main()
//     {
//         var program = new Program();
//         program.Init();
//         bool result = program.Process();
//         Console.WriteLine($"处理结果: {result}");
//     }
// }