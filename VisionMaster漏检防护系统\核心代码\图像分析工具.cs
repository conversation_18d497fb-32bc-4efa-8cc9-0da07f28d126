// 图像分析工具 - 用于分析存储的产品图像，查找潜在漏检
using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using System.Text;

public class ImageAnalysisTool
{
    private const string LOG_PATH = @"D:\HODD\DetectionLog.txt";
    private const string ANALYSIS_RESULT_PATH = @"D:\HODD\AnalysisResult.txt";
    private const double SIMILARITY_THRESHOLD = 2.0;
    
    public class DetectionRecord
    {
        public DateTime DetectionTime { get; set; }
        public string ProductId { get; set; }
        public string Result { get; set; }
        public double MaxDis { get; set; }
        public double[] DisValues { get; set; }
        public string ImagePath { get; set; }
    }
    
    // 分析所有检测记录，查找潜在漏检
    public void AnalyzeDetectionHistory()
    {
        try
        {
            Console.WriteLine("开始分析检测历史记录...");
            
            // 读取检测日志
            var records = LoadDetectionRecords();
            Console.WriteLine("加载了 " + records.Count + " 条检测记录");

            // 分离OK和NG产品
            var okProducts = records.Where(r => r.Result == "OK").ToList();
            var ngProducts = records.Where(r => r.Result == "NG").ToList();

            Console.WriteLine("OK产品: " + okProducts.Count + " 个");
            Console.WriteLine("NG产品: " + ngProducts.Count + " 个");
            
            // 查找疑似漏检
            var suspiciousProducts = FindSuspiciousProducts(okProducts, ngProducts);
            
            // 生成分析报告
            GenerateAnalysisReport(suspiciousProducts, okProducts.Count, ngProducts.Count);
            
            Console.WriteLine("分析完成，发现 " + suspiciousProducts.Count + " 个疑似漏检产品");
            Console.WriteLine("详细报告已保存到: " + ANALYSIS_RESULT_PATH);
        }
        catch (Exception ex)
        {
            Console.WriteLine("分析过程出错: " + ex.Message);
        }
    }
    
    // 加载检测记录
    private List<DetectionRecord> LoadDetectionRecords()
    {
        var records = new List<DetectionRecord>();
        
        if (!File.Exists(LOG_PATH))
        {
            Console.WriteLine("日志文件不存在: " + LOG_PATH);
            return records;
        }
        
        var lines = File.ReadAllLines(LOG_PATH);
        
        foreach (var line in lines)
        {
            try
            {
                var parts = line.Split(',');
                if (parts.Length >= 6)
                {
                    var record = new DetectionRecord
                    {
                        DetectionTime = DateTime.Parse(parts[0]),
                        ProductId = parts[1],
                        Result = parts[2],
                        MaxDis = double.Parse(parts[3]),
                        DisValues = parts[4].Split(';').Select(double.Parse).ToArray(),
                        ImagePath = parts[5]
                    };
                    records.Add(record);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("解析日志行失败: " + line + ", 错误: " + ex.Message);
            }
        }
        
        return records.OrderBy(r => r.DetectionTime).ToList();
    }
    
    // 查找疑似漏检产品
    private List<SuspiciousProduct> FindSuspiciousProducts(List<DetectionRecord> okProducts, List<DetectionRecord> ngProducts)
    {
        var suspiciousProducts = new List<SuspiciousProduct>();
        
        Console.WriteLine("正在分析产品相似性...");
        
        foreach (var okProduct in okProducts)
        {
            // 查找与此OK产品相似的NG产品
            var similarNGProducts = new List<(DetectionRecord ng, double similarity)>();
            
            foreach (var ngProduct in ngProducts)
            {
                double similarity = CalculateDisSimilarity(okProduct.DisValues, ngProduct.DisValues);
                
                if (similarity < SIMILARITY_THRESHOLD)
                {
                    similarNGProducts.Add((ngProduct, similarity));
                }
            }
            
            if (similarNGProducts.Any())
            {
                // 找到最相似的NG产品
                var mostSimilar = similarNGProducts.OrderBy(x => x.similarity).First();
                
                suspiciousProducts.Add(new SuspiciousProduct
                {
                    OKProduct = okProduct,
                    SimilarNGProduct = mostSimilar.ng,
                    Similarity = mostSimilar.similarity,
                    DisThresholdExceeded = mostSimilar.ng.MaxDis,
                    SuspicionLevel = CalculateSuspicionLevel(mostSimilar.similarity, okProduct.MaxDis, mostSimilar.ng.MaxDis)
                });
            }
        }
        
        return suspiciousProducts.OrderBy(s => s.Similarity).ToList();
    }
    
    // 计算dis参数相似度
    private double CalculateDisSimilarity(double[] dis1, double[] dis2)
    {
        if (dis1.Length != dis2.Length)
            return double.MaxValue;
        
        double sumSquaredDiff = 0;
        for (int i = 0; i < dis1.Length; i++)
        {
            double diff = dis1[i] - dis2[i];
            sumSquaredDiff += diff * diff;
        }
        
        return Math.Sqrt(sumSquaredDiff / dis1.Length);
    }
    
    // 计算疑似程度
    private string CalculateSuspicionLevel(double similarity, double okMaxDis, double ngMaxDis)
    {
        double disRatio = okMaxDis / ngMaxDis;
        
        if (similarity < 0.5 && disRatio > 0.95)
            return "高度疑似";
        else if (similarity < 1.0 && disRatio > 0.90)
            return "中度疑似";
        else
            return "低度疑似";
    }
    
    // 生成分析报告
    private void GenerateAnalysisReport(List<SuspiciousProduct> suspiciousProducts, int totalOK, int totalNG)
    {
        var report = new StringBuilder();

        report.AppendLine("=== 产品检测漏检分析报告 ===");
        report.AppendLine("分析时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        report.AppendLine("总检测产品数: " + (totalOK + totalNG));
        report.AppendLine("OK产品数: " + totalOK);
        report.AppendLine("NG产品数: " + totalNG);
        report.AppendLine("疑似漏检产品数: " + suspiciousProducts.Count);
        report.AppendLine("漏检率估算: " + ((double)suspiciousProducts.Count / totalOK * 100).ToString("F2") + "%");
        report.AppendLine();

        if (suspiciousProducts.Any())
        {
            // 按疑似程度排序，高度疑似排在前面
            var sortedSuspicious = suspiciousProducts
                .OrderBy(s => s.SuspicionLevel == "高度疑似" ? 1 : s.SuspicionLevel == "中度疑似" ? 2 : 3)
                .ThenBy(s => s.Similarity)
                .ToList();

            report.AppendLine("=== 🚨 高优先级疑似漏检产品（建议立即检查）===");
            var highPriority = sortedSuspicious.Where(s => s.SuspicionLevel == "高度疑似").ToList();
            if (highPriority.Any())
            {
                foreach (var sp in highPriority)
                {
                    report.AppendLine("🔴 产品ID: " + sp.OKProduct.ProductId);
                    report.AppendLine("   检测时间: " + sp.OKProduct.DetectionTime.ToString("yyyy-MM-dd HH:mm:ss"));
                    report.AppendLine("   相似度: " + sp.Similarity.ToString("F3") + " (极高相似)");
                    report.AppendLine("   OK产品最大dis: " + sp.OKProduct.MaxDis.ToString("F3"));
                    report.AppendLine("   相似NG产品最大dis: " + sp.SimilarNGProduct.MaxDis.ToString("F3"));
                    report.AppendLine("   图像路径: " + sp.OKProduct.ImagePath);
                    report.AppendLine("   参考NG图像: " + sp.SimilarNGProduct.ImagePath);
                    report.AppendLine();
                }
            }
            else
            {
                report.AppendLine("   无高度疑似产品");
            }

            report.AppendLine("=== ⚠️  中优先级疑似漏检产品 ===");
            var mediumPriority = sortedSuspicious.Where(s => s.SuspicionLevel == "中度疑似").ToList();
            if (mediumPriority.Any())
            {
                foreach (var sp in mediumPriority)
                {
                    report.AppendLine("🟡 产品ID: " + sp.OKProduct.ProductId + " | 相似度: " + sp.Similarity.ToString("F3") + " | 图像: " + sp.OKProduct.ImagePath);
                }
            }
            else
            {
                report.AppendLine("   无中度疑似产品");
            }

            report.AppendLine();
            report.AppendLine("=== 📋 完整疑似产品列表 ===");
            report.AppendLine("序号\t疑似程度\t相似度\tOK产品ID\t\tNG产品ID\t\tOK最大dis\tNG最大dis\tOK图像路径");

            for (int i = 0; i < sortedSuspicious.Count; i++)
            {
                var sp = sortedSuspicious[i];
                report.AppendLine((i + 1) + "\t" + sp.SuspicionLevel + "\t" + sp.Similarity.ToString("F3") + "\t" +
                               sp.OKProduct.ProductId + "\t" + sp.SimilarNGProduct.ProductId + "\t" +
                               sp.OKProduct.MaxDis.ToString("F3") + "\t\t" + sp.SimilarNGProduct.MaxDis.ToString("F3") + "\t\t" +
                               sp.OKProduct.ImagePath);
            }

            report.AppendLine();
            report.AppendLine("=== 🎯 精准定位建议 ===");
            report.AppendLine("1. 优先检查🔴标记的高度疑似产品");
            report.AppendLine("2. 使用生成的图像对比脚本快速验证");
            report.AppendLine("3. 检查dis参数详细差异分析");
            report.AppendLine("4. 考虑调整检测阈值或算法参数");
        }
        else
        {
            report.AppendLine("✅ 未发现疑似漏检产品。");
        }

        File.WriteAllText(ANALYSIS_RESULT_PATH, report.ToString(), Encoding.UTF8);

        // 生成图像对比脚本
        GenerateImageComparisonScript(suspiciousProducts);

        // 生成详细dis分析
        GenerateDetailedDisAnalysis(suspiciousProducts);
    }
    
    // 按日期分析
    public void AnalyzeByDate(DateTime startDate, DateTime endDate)
    {
        Console.WriteLine("分析时间段: " + startDate.ToString("yyyy-MM-dd") + " 到 " + endDate.ToString("yyyy-MM-dd"));

        var allRecords = LoadDetectionRecords();
        var filteredRecords = allRecords.Where(r => r.DetectionTime >= startDate && r.DetectionTime <= endDate).ToList();

        Console.WriteLine("时间段内检测记录: " + filteredRecords.Count + " 条");
        
        var okProducts = filteredRecords.Where(r => r.Result == "OK").ToList();
        var ngProducts = filteredRecords.Where(r => r.Result == "NG").ToList();
        
        var suspiciousProducts = FindSuspiciousProducts(okProducts, ngProducts);
        
        string dateRangeReport = ANALYSIS_RESULT_PATH.Replace(".txt", "_" + startDate.ToString("yyyyMMdd") + "_" + endDate.ToString("yyyyMMdd") + ".txt");
        GenerateAnalysisReport(suspiciousProducts, okProducts.Count, ngProducts.Count);

        Console.WriteLine("时间段分析完成，报告保存到: " + dateRangeReport);
    }
    
    // 生成图像对比脚本
    private void GenerateImageComparisonScript(List<SuspiciousProduct> suspiciousProducts)
    {
        if (!suspiciousProducts.Any()) return;

        var scriptPath = ANALYSIS_RESULT_PATH.Replace(".txt", "_图像对比.bat");
        var script = new StringBuilder();

        script.AppendLine("@echo off");
        script.AppendLine("chcp 65001 >nul");
        script.AppendLine("echo ========================================");
        script.AppendLine("echo 疑似漏检产品图像对比工具");
        script.AppendLine("echo ========================================");
        script.AppendLine("echo.");

        var highPriority = suspiciousProducts.Where(s => s.SuspicionLevel == "高度疑似").ToList();

        if (highPriority.Any())
        {
            script.AppendLine("echo 🔴 高度疑似产品图像对比：");
            script.AppendLine("echo.");

            for (int i = 0; i < highPriority.Count; i++)
            {
                var sp = highPriority[i];
                script.AppendLine("echo " + (i + 1) + ". 产品ID: " + sp.OKProduct.ProductId);
                script.AppendLine("echo    相似度: " + sp.Similarity.ToString("F3"));
                script.AppendLine("echo    正在打开图像对比...");
                script.AppendLine("start \"\" \"" + sp.OKProduct.ImagePath + "\"");
                script.AppendLine("start \"\" \"" + sp.SimilarNGProduct.ImagePath + "\"");
                script.AppendLine("echo    请对比两张图像，确认是否为漏检");
                script.AppendLine("pause");
                script.AppendLine("echo.");
            }
        }

        script.AppendLine("echo 图像对比完成！");
        script.AppendLine("pause");

        File.WriteAllText(scriptPath, script.ToString(), Encoding.UTF8);
        Console.WriteLine("图像对比脚本已生成: " + scriptPath);
    }

    // 生成详细dis分析
    private void GenerateDetailedDisAnalysis(List<SuspiciousProduct> suspiciousProducts)
    {
        if (!suspiciousProducts.Any()) return;

        var analysisPath = ANALYSIS_RESULT_PATH.Replace(".txt", "_详细dis分析.txt");
        var analysis = new StringBuilder();

        analysis.AppendLine("=== 疑似漏检产品dis参数详细分析 ===");
        analysis.AppendLine("分析时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        analysis.AppendLine();

        foreach (var sp in suspiciousProducts.OrderBy(s => s.Similarity))
        {
            analysis.AppendLine("🔍 产品对比分析 - " + sp.OKProduct.ProductId + " vs " + sp.SimilarNGProduct.ProductId);
            analysis.AppendLine("相似度: " + sp.Similarity.ToString("F3") + " | 疑似程度: " + sp.SuspicionLevel);
            analysis.AppendLine();

            analysis.AppendLine("钩角位置\tOK产品dis\tNG产品dis\t差值\t\t状态");
            analysis.AppendLine("--------\t---------\t---------\t----\t\t----");

            for (int i = 0; i < sp.OKProduct.DisValues.Length && i < sp.SimilarNGProduct.DisValues.Length; i++)
            {
                double okDis = sp.OKProduct.DisValues[i];
                double ngDis = sp.SimilarNGProduct.DisValues[i];
                double diff = Math.Abs(okDis - ngDis);
                string status = diff < 0.1 ? "极相似" : diff < 0.5 ? "相似" : "差异";

                analysis.AppendLine("钩角" + (i + 1).ToString("D2") + "\t\t" + okDis.ToString("F3") + "\t\t" + ngDis.ToString("F3") + "\t\t" + diff.ToString("F3") + "\t\t" + status);
            }

            analysis.AppendLine();
            analysis.AppendLine("OK产品最大dis: " + sp.OKProduct.MaxDis.ToString("F3"));
            analysis.AppendLine("NG产品最大dis: " + sp.SimilarNGProduct.MaxDis.ToString("F3"));
            analysis.AppendLine("最大dis差值: " + Math.Abs(sp.OKProduct.MaxDis - sp.SimilarNGProduct.MaxDis).ToString("F3"));
            analysis.AppendLine();
            analysis.AppendLine("📸 图像路径:");
            analysis.AppendLine("OK产品: " + sp.OKProduct.ImagePath);
            analysis.AppendLine("NG产品: " + sp.SimilarNGProduct.ImagePath);
            analysis.AppendLine();
            analysis.AppendLine("".PadRight(80, '='));
            analysis.AppendLine();
        }

        File.WriteAllText(analysisPath, analysis.ToString(), Encoding.UTF8);
        Console.WriteLine("详细dis分析已生成: " + analysisPath);
    }

    // 智能漏检定位 - 新增功能
    public void SmartMissedDetectionLocator()
    {
        try
        {
            Console.WriteLine("🎯 启动智能漏检定位系统...");

            var records = LoadDetectionRecords();
            var okProducts = records.Where(r => r.Result == "OK").ToList();
            var ngProducts = records.Where(r => r.Result == "NG").ToList();

            Console.WriteLine("分析数据: OK产品 " + okProducts.Count + " 个, NG产品 " + ngProducts.Count + " 个");

            // 使用更严格的相似度阈值进行精准定位
            var originalThreshold = SIMILARITY_THRESHOLD;
            var strictThreshold = 1.0; // 更严格的阈值

            var suspiciousProducts = FindSuspiciousProductsWithThreshold(okProducts, ngProducts, strictThreshold);

            if (suspiciousProducts.Any())
            {
                Console.WriteLine("🚨 发现 " + suspiciousProducts.Count + " 个高度疑似漏检产品！");

                // 按相似度排序，最相似的排在前面
                var sortedResults = suspiciousProducts.OrderBy(s => s.Similarity).ToList();

                Console.WriteLine("\n🔴 最可能的漏检产品（按相似度排序）：");
                for (int i = 0; i < Math.Min(5, sortedResults.Count); i++)
                {
                    var sp = sortedResults[i];
                    Console.WriteLine((i + 1) + ". 产品ID: " + sp.OKProduct.ProductId);
                    Console.WriteLine("   检测时间: " + sp.OKProduct.DetectionTime.ToString("yyyy-MM-dd HH:mm:ss"));
                    Console.WriteLine("   相似度: " + sp.Similarity.ToString("F3") + " (数值越小越相似)");
                    Console.WriteLine("   图像路径: " + sp.OKProduct.ImagePath);
                    Console.WriteLine("   参考NG图像: " + sp.SimilarNGProduct.ImagePath);
                    Console.WriteLine();
                }

                // 生成精准定位报告
                GeneratePreciseLocationReport(sortedResults);

                // 自动打开最可能的漏检产品图像
                if (sortedResults.Any())
                {
                    Console.Write("是否自动打开最可能的漏检产品图像进行确认？(Y/N): ");
                    string response = Console.ReadLine();
                    if (response?.ToUpper() == "Y")
                    {
                        AutoOpenSuspiciousImages(sortedResults.Take(3).ToList());
                    }
                }
            }
            else
            {
                Console.WriteLine("✅ 未发现高度疑似的漏检产品");

                // 尝试更宽松的阈值
                Console.WriteLine("🔍 尝试更宽松的检测条件...");
                var relaxedSuspicious = FindSuspiciousProductsWithThreshold(okProducts, ngProducts, 2.0);

                if (relaxedSuspicious.Any())
                {
                    Console.WriteLine("⚠️  发现 " + relaxedSuspicious.Count + " 个中度疑似产品");
                    GeneratePreciseLocationReport(relaxedSuspicious.OrderBy(s => s.Similarity).ToList());
                }
                else
                {
                    Console.WriteLine("✅ 系统运行良好，未发现疑似漏检");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("❌ 智能定位过程出错: " + ex.Message);
        }
    }

    // 使用指定阈值查找疑似产品
    private List<SuspiciousProduct> FindSuspiciousProductsWithThreshold(List<DetectionRecord> okProducts, List<DetectionRecord> ngProducts, double threshold)
    {
        var suspiciousProducts = new List<SuspiciousProduct>();

        foreach (var okProduct in okProducts)
        {
            foreach (var ngProduct in ngProducts)
            {
                double similarity = CalculateDisSimilarity(okProduct.DisValues, ngProduct.DisValues);

                if (similarity < threshold)
                {
                    suspiciousProducts.Add(new SuspiciousProduct
                    {
                        OKProduct = okProduct,
                        SimilarNGProduct = ngProduct,
                        Similarity = similarity,
                        DisThresholdExceeded = ngProduct.MaxDis,
                        SuspicionLevel = CalculateSuspicionLevel(similarity, okProduct.MaxDis, ngProduct.MaxDis)
                    });
                }
            }
        }

        // 去重：每个OK产品只保留最相似的NG产品
        return suspiciousProducts
            .GroupBy(s => s.OKProduct.ProductId)
            .Select(g => g.OrderBy(s => s.Similarity).First())
            .ToList();
    }

    // 生成精准定位报告
    private void GeneratePreciseLocationReport(List<SuspiciousProduct> suspiciousProducts)
    {
        var reportPath = ANALYSIS_RESULT_PATH.Replace(".txt", "_精准定位报告.txt");
        var report = new StringBuilder();

        report.AppendLine("=== 🎯 精准漏检定位报告 ===");
        report.AppendLine("生成时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        report.AppendLine("疑似漏检产品数量: " + suspiciousProducts.Count);
        report.AppendLine();

        report.AppendLine("🔴 按相似度排序的疑似漏检产品（数值越小越可疑）：");
        report.AppendLine();

        for (int i = 0; i < suspiciousProducts.Count; i++)
        {
            var sp = suspiciousProducts[i];
            string priority = i < 3 ? "🔴 极高" : i < 6 ? "🟡 高" : "🟢 中";

            report.AppendLine((i + 1) + ". " + priority + " 优先级");
            report.AppendLine("   产品ID: " + sp.OKProduct.ProductId);
            report.AppendLine("   检测时间: " + sp.OKProduct.DetectionTime.ToString("yyyy-MM-dd HH:mm:ss"));
            report.AppendLine("   相似度: " + sp.Similarity.ToString("F3"));
            report.AppendLine("   疑似程度: " + sp.SuspicionLevel);
            report.AppendLine("   OK产品最大dis: " + sp.OKProduct.MaxDis.ToString("F3"));
            report.AppendLine("   NG产品最大dis: " + sp.SimilarNGProduct.MaxDis.ToString("F3"));
            report.AppendLine("   图像路径: " + sp.OKProduct.ImagePath);
            report.AppendLine("   参考NG图像: " + sp.SimilarNGProduct.ImagePath);
            report.AppendLine();
        }

        report.AppendLine("=== 🛠️ 处理建议 ===");
        report.AppendLine("1. 优先检查🔴极高优先级的产品");
        report.AppendLine("2. 使用生成的图像对比脚本快速验证");
        report.AppendLine("3. 查看详细dis分析了解具体差异");
        report.AppendLine("4. 确认漏检后调整检测参数");

        File.WriteAllText(reportPath, report.ToString(), Encoding.UTF8);
        Console.WriteLine("📋 精准定位报告已生成: " + reportPath);
    }

    // 自动打开疑似图像
    private void AutoOpenSuspiciousImages(List<SuspiciousProduct> topSuspicious)
    {
        try
        {
            Console.WriteLine("🖼️ 正在打开疑似漏检产品图像...");

            foreach (var sp in topSuspicious)
            {
                Console.WriteLine("打开产品 " + sp.OKProduct.ProductId + " 的图像对比");

                if (File.Exists(sp.OKProduct.ImagePath))
                {
                    System.Diagnostics.Process.Start("explorer.exe", sp.OKProduct.ImagePath);
                }

                if (File.Exists(sp.SimilarNGProduct.ImagePath))
                {
                    System.Diagnostics.Process.Start("explorer.exe", sp.SimilarNGProduct.ImagePath);
                }

                Console.WriteLine("已打开: " + sp.OKProduct.ProductId + " (相似度: " + sp.Similarity.ToString("F3") + ")");
                Console.WriteLine("请对比图像确认是否为漏检，按任意键继续下一个...");
                Console.ReadKey();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("❌ 打开图像失败: " + ex.Message);
        }
    }

    public class SuspiciousProduct
    {
        public DetectionRecord OKProduct { get; set; }
        public DetectionRecord SimilarNGProduct { get; set; }
        public double Similarity { get; set; }
        public double DisThresholdExceeded { get; set; }
        public string SuspicionLevel { get; set; }
    }
}

// 主程序入口
public class Program
{
    public static void Main(string[] args)
    {
        var analyzer = new ImageAnalysisTool();

        Console.WriteLine("=== 🎯 产品检测漏检分析工具 ===");
        Console.WriteLine("1. 智能漏检精准定位 (推荐)");
        Console.WriteLine("2. 分析全部历史记录");
        Console.WriteLine("3. 分析指定日期范围");
        Console.Write("请选择分析模式 (1-3): ");

        string choice = Console.ReadLine();

        switch (choice)
        {
            case "1":
                analyzer.SmartMissedDetectionLocator();
                break;
            case "2":
                analyzer.AnalyzeDetectionHistory();
                break;
            case "3":
                Console.Write("请输入开始日期 (yyyy-MM-dd): ");
                DateTime startDate = DateTime.Parse(Console.ReadLine());

                Console.Write("请输入结束日期 (yyyy-MM-dd): ");
                DateTime endDate = DateTime.Parse(Console.ReadLine());

                analyzer.AnalyzeByDate(startDate, endDate);
                break;
            default:
                Console.WriteLine("使用默认模式：智能漏检精准定位...");
                analyzer.SmartMissedDetectionLocator();
                break;
        }

        Console.WriteLine("\n分析完成！按任意键退出...");
        Console.ReadKey();
    }
}
