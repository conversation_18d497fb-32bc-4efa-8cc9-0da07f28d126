{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "E:\\soft\\opencv\\opencv\\build\\x64\\test\\install\\include", "E:\\soft\\opencv\\opencv\\build\\x64\\test\\install\\include\\opencv2"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"], "compilerPath": "E:\\soft\\mingGW\\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\\mingw64\\bin\\gcc.exe", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "windows-gcc-x64"}], "version": 4}