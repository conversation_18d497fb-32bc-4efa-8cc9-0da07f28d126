配置文件转换工具 v2.1.1 - 安全补丁版本
=====================================

版本信息：
- 版本号：v2.1.1
- 发布日期：2025年7月10日
- 文件大小：约 139 MB
- 支持系统：Windows 10/11 (64位)

安全更新：
=========

🔒 **重要安全补丁**
本版本主要修复了重要的安全漏洞，强烈建议所有用户升级。

✅ **System.Text.Json 安全更新**
- 更新版本：从 7.0.0 升级到 8.0.5
- 修复漏洞：GHSA-hh2w-p6rv-4g7w, GHSA-8g4q-xg66-9fp4
- 安全等级：高危漏洞修复
- 影响范围：JSON序列化和反序列化安全性

✅ **依赖项安全加固**
- 更新所有依赖项到最新安全版本
- 移除已知安全漏洞的组件
- 加强输入验证和数据处理安全性

功能保持：
=========

🔧 **完全兼容**
- 所有功能与 v2.1.0 完全相同
- 数据提取过程保持不变
- GUI界面和操作方式无变化
- 输入输出格式完全兼容

🔧 **核心功能**
- 三步骤数据提取过程
- 字段定义驱动的精确处理
- 目标格式字段比对和映射
- 智能的默认值和舍弃处理
- 详细的转换过程日志
- 增强的GUI对比表格

安全改进详情：
=============

🛡️ **JSON处理安全性**
- 修复了JSON反序列化中的潜在安全漏洞
- 加强了对恶意JSON输入的防护
- 提升了大文件处理的稳定性

🛡️ **数据验证增强**
- 更严格的输入数据验证
- 防止潜在的数据注入攻击
- 加强了文件路径验证

🛡️ **内存安全**
- 优化了内存使用和释放
- 防止潜在的内存泄漏
- 提升了大数据量处理的稳定性

升级建议：
=========

⚠️ **强烈建议升级**
- 本版本修复了高危安全漏洞
- 建议所有用户立即升级到 v2.1.1
- 特别是在生产环境中使用的用户

⚠️ **升级步骤**
1. 备份当前配置文件
2. 下载新版本安装包
3. 替换旧版本程序
4. 验证功能正常运行

⚠️ **兼容性**
- 与 v2.1.0 完全兼容
- 无需修改配置文件
- 无需重新学习操作方式

使用方法：
=========

使用方法与 v2.1.0 完全相同：

1. **GUI模式**（推荐）
   - 双击ConfigConverter.exe启动
   - 选择源文件：表格配置参数.txt
   - 选择目标文件：输出的json文件
   - 设置型号名称和片数
   - 点击"转换"按钮
   - 查看详细的转换日志和对比表格

2. **命令行模式**
   ```
   ConfigConverter.exe --console 源文件.txt 目标文件.json 型号名称 片数
   ```

文件清单：
=========

release_v2.1.1/ 目录包含：
- ConfigConverter.exe          # 主程序（v2.1.1，安全补丁版本）
- 表格配置参数.txt             # 输入示例文件
- 检测信息字段.json            # 字段定义文件（必需）
- 0523A-F_示例.json            # 输出格式示例
- v2.1.1安全补丁版本说明.txt   # 本文件

技术规格：
=========

🔧 **安全特性**
- 最新的安全依赖项
- 加强的输入验证
- 安全的JSON处理
- 内存安全保护

🔧 **性能特性**
- 优化的内存使用
- 更快的JSON处理
- 稳定的大文件处理
- 改进的错误恢复

🔧 **兼容性**
- Windows 10/11 (64位)
- .NET 6.0 运行时（内置）
- 完全向后兼容

重要提醒：
=========

⚠️ **安全重要性**
- 本版本修复了高危安全漏洞
- 强烈建议立即升级
- 不建议继续使用旧版本

⚠️ **生产环境**
- 生产环境必须升级到此版本
- 建议在升级前进行测试
- 确保备份重要配置文件

⚠️ **网络安全**
- 如果程序处理来自网络的数据，升级尤为重要
- 新版本提供更好的安全防护
- 建议配合防火墙和安全策略使用

技术支持：
=========

如遇到问题：
1. 确认使用的是 v2.1.1 版本
2. 检查所有必需文件是否存在
3. 查看程序日志获取详细信息
4. 参考示例文件确认格式

版本历史：
=========

v2.1.1 (2025-07-10)
- 🔒 修复重要安全漏洞
- 🔒 更新System.Text.Json到8.0.5
- 🔒 加强数据验证和安全性
- ✨ 保持所有功能完全兼容

v2.1.0 (2025-07-10)
- ✨ 实现完整的三步骤数据提取过程
- ✨ 精确的字段比对和默认值处理
- ✨ 多余字段自动舍弃功能

v2.0.0 (2025-07-10)
- 🔥 完全重新设计和实现
- ✨ 支持新的0523A-F.json格式

---

配置文件转换工具 v2.1.1
安全第一，功能完整，值得信赖
