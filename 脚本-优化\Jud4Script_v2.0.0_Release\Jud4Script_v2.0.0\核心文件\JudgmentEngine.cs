using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Jud4Script
{
    /// <summary>
    /// 调试日志记录器
    /// </summary>
    public static class DebugLogger
    {
        private static StringBuilder _logBuffer = new StringBuilder();
        private static bool _debugEnabled = true;

        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">调试信息</param>
        public static void Log(string message)
        {
            if (_debugEnabled)
            {
                string timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
                _logBuffer.AppendLine("[" + timestamp + "] " + message);
            }
        }

        /// <summary>
        /// 记录错误信息
        /// </summary>
        /// <param name="location">错误位置</param>
        /// <param name="errorType">错误类型</param>
        /// <param name="details">错误详情</param>
        /// <returns>格式化的错误信息</returns>
        public static string LogError(string location, string errorType, string details)
        {
            string errorMessage = string.Format("[ERROR] {0} - {1}: {2}", location, errorType, details);
            Log(errorMessage);
            return errorMessage;
        }

        /// <summary>
        /// 获取所有日志信息
        /// </summary>
        /// <returns>日志内容</returns>
        public static string GetLogs()
        {
            return _logBuffer.ToString();
        }

        /// <summary>
        /// 清空日志
        /// </summary>
        public static void ClearLogs()
        {
            _logBuffer.Clear();
        }

        /// <summary>
        /// 启用或禁用调试
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public static void SetDebugEnabled(bool enabled)
        {
            _debugEnabled = enabled;
        }
    }
    /// <summary>
    /// 判定引擎，提供产品检测判定功能
    /// </summary>
    public static class JudgmentEngine
    {
        /// <summary>
        /// 主要评估函数，根据新的设计要求实现
        /// </summary>
        /// <param name="modelNo">产品型号</param>
        /// <param name="inspecInfo">检测信息，格式：Items-数量;项目名V测量值;...</param>
        /// <returns>出料分类字符串</returns>
        public static string Evaluate(string modelNo, string inspecInfo)
        {
            try
            {
                // 清空之前的日志
                DebugLogger.ClearLogs();
                DebugLogger.Log("开始判定流程");
                DebugLogger.Log("输入参数 - 产品型号: " + (modelNo ?? "null"));
                DebugLogger.Log("输入参数 - 检测信息: " + (inspecInfo ?? "null"));
                // 1. 从缓存或文件加载配置
                DebugLogger.Log("步骤1: 开始加载配置文件");
                DebugLogger.Log("尝试加载产品型号: " + modelNo);
                var configDict = ConfigManager.GetModelConfig(modelNo);
                DebugLogger.Log("配置加载结果: 找到 " + configDict.Count + " 个检测项配置");
                if (configDict.Count == 0)
                {
                    string errorMsg = DebugLogger.LogError("配置加载", "配置文件为空", "产品型号 '" + modelNo + "' 对应的配置文件不存在或为空");
                    DebugLogger.Log("判定结果: 0 (异常分类) - " + errorMsg);
                    return "0"; // 异常分类：配置加载失败
                }
                DebugLogger.Log("配置加载成功");

                // 2. 解析输入信息
                DebugLogger.Log("步骤2: 开始解析输入信息");
                DebugLogger.Log("输入信息长度: " + inspecInfo.Length + " 字符");
                var parseResult = ParseInspecInfo(inspecInfo);
                if (!parseResult.IsValid)
                {
                    string errorMsg = DebugLogger.LogError("输入解析", "格式错误", "输入信息格式不正确，无法解析Items字段或检测项格式错误");
                    DebugLogger.Log("判定结果: 0 (异常分类) - " + errorMsg);
                    return "0"; // 异常分类：输入格式错误
                }
                DebugLogger.Log("输入解析成功，找到 " + parseResult.Items.Count + " 个检测项");

                // 3. 对每个检测项进行判定
                DebugLogger.Log("步骤3: 开始检测项判定");
                var itemResults = new List<ItemJudgmentResult>();
                foreach (var item in parseResult.Items)
                {
                    DebugLogger.Log("处理检测项: " + item.Key + " = " + item.Value);
                    // 使用新的EvaluateItem方法，它包含了所有的验证逻辑
                    var result = EvaluateItem(item.Key, item.Value, configDict);
                    DebugLogger.Log("检测项 '" + item.Key + "' 判定结果: " + result.Judgment +
                                  (string.IsNullOrEmpty(result.ErrorMessage) ? "" : " (错误: " + result.ErrorMessage + ")"));
                    itemResults.Add(result);
                }
                DebugLogger.Log("所有检测项判定完成");

                // 4. 确定最终判定分类和出料分类
                DebugLogger.Log("步骤4: 开始确定最终判定结果");
                var finalResult = DetermineFinalResult(itemResults);
                DebugLogger.Log("最终判定结果: " + finalResult);

                // 根据最终结果返回出料口
                if (finalResult.StartsWith("出口"))
                {
                    string outputPort = finalResult.Substring(finalResult.Length - 1);
                    DebugLogger.Log("返回出料口: " + outputPort);
                    DebugLogger.Log("判定流程完成");
                    return outputPort;
                }

                DebugLogger.Log("返回最终结果: " + finalResult);
                DebugLogger.Log("判定流程完成");
                return finalResult;
            }
            catch (Exception ex)
            {
                string errorMsg = DebugLogger.LogError("系统异常", "未处理异常",
                    "异常类型: " + ex.GetType().Name + ", 异常信息: " + ex.Message +
                    ", 堆栈跟踪: " + ex.StackTrace);
                DebugLogger.Log("判定结果: 0 (异常分类) - " + errorMsg);
                Console.WriteLine("判定过程发生异常: " + ex.Message);
                Console.WriteLine("详细调试信息:");
                Console.WriteLine(DebugLogger.GetLogs());
                return "0"; // 异常分类
            }
        }

        /// <summary>
        /// 解析检测信息字符串
        /// </summary>
        /// <param name="inspecInfo">检测信息字符串</param>
        /// <returns>解析结果</returns>
        private static ParseResult ParseInspecInfo(string inspecInfo)
        {
            var result = new ParseResult();
            result.Items = new Dictionary<string, string>(); // 初始化字典

            if (string.IsNullOrEmpty(inspecInfo))
            {
                result.IsValid = false;
                return result;
            }

            try
            {
                // 查找"Items="字段的位置
                int itemsStartIndex = inspecInfo.IndexOf("Items=");
                if (itemsStartIndex == -1)
                {
                    result.IsValid = false;
                    return result;
                }

                // 获取 "Items=" 后面的所有内容
                string itemsData = inspecInfo.Substring(itemsStartIndex + 6); // 跳过"Items="
                string[] parts = itemsData.Split(';');

                result.Items = new Dictionary<string, string>();
                foreach (var part in parts)
                {
                    if (string.IsNullOrEmpty(part)) continue;

                    int equalIndex = part.LastIndexOf('=');
                    if (equalIndex <= 0) continue; // =必须存在且不能在开头

                    string itemNameWithCount = part.Substring(0, equalIndex).Trim();
                    string measureValue = part.Substring(equalIndex + 1).Trim();

                    if (!string.IsNullOrEmpty(itemNameWithCount))
                    {
                        // 解析检测项名称，可能包含*n格式
                        string itemName = ParseItemName(itemNameWithCount);
                        if (!string.IsNullOrEmpty(itemName))
                        {
                            // 只有包含*n格式时才保存COUNT信息
                            string countInfo = itemNameWithCount.Contains("*") ? itemNameWithCount : "";
                            result.Items[itemName] = measureValue + "|COUNT:" + countInfo;
                        }
                    }
                }
                result.IsValid = true;
                return result;
            }
            catch (Exception)
            {
                result.IsValid = false;
                return result;
            }
        }

        /// <summary>
        /// 解析检测项名称，支持*n格式
        /// </summary>
        /// <param name="itemNameWithCount">可能包含*n的检测项名称</param>
        /// <returns>纯检测项名称</returns>
        private static string ParseItemName(string itemNameWithCount)
        {
            if (string.IsNullOrEmpty(itemNameWithCount))
                return string.Empty;

            // 查找*号的位置
            int asteriskIndex = itemNameWithCount.IndexOf('*');
            if (asteriskIndex > 0)
            {
                // 返回*号之前的部分作为检测项名称
                return itemNameWithCount.Substring(0, asteriskIndex).Trim();
            }
            else
            {
                // 没有*号，直接返回原名称
                return itemNameWithCount.Trim();
            }
        }

        /// <summary>
        /// 解析测量值和数量信息
        /// </summary>
        /// <param name="measureValueStr">测量值字符串，格式：值1,值2,值3|COUNT:检测项*n</param>
        /// <returns>解析结果</returns>
        private static MeasureValueParseResult ParseMeasureValueWithCount(string measureValueStr)
        {
            var result = new MeasureValueParseResult();

            try
            {
                // 分离测量值和数量信息
                string[] parts = measureValueStr.Split(new string[] { "|COUNT:" }, 2, StringSplitOptions.None);
                string valuesStr = parts[0];
                string countInfo = parts.Length > 1 ? parts[1] : "";

                // 解析测量值
                if (string.IsNullOrEmpty(valuesStr))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "测量值为空";
                    return result;
                }

                string[] valueStrings = valuesStr.Split(',');
                // 过滤掉空字符串
                var validValues = new System.Collections.Generic.List<string>();
                foreach (string value in valueStrings)
                {
                    if (!string.IsNullOrEmpty(value.Trim()))
                    {
                        validValues.Add(value.Trim());
                    }
                }
                result.MeasureValues = validValues.ToArray();
                result.ActualCount = validValues.Count;

                // 验证单值多测量值限制（没有*n格式但有多个测量值）
                if (string.IsNullOrEmpty(countInfo) && result.ActualCount > 1)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "单值情况不允许多个测量值，应使用*n格式或单个测量值";
                    return result;
                }

                // 解析期望数量
                if (!string.IsNullOrEmpty(countInfo))
                {
                    int asteriskIndex = countInfo.IndexOf('*');
                    if (asteriskIndex > 0 && asteriskIndex < countInfo.Length - 1)
                    {
                        string countStr = countInfo.Substring(asteriskIndex + 1);
                        int expectedCount;
                        if (int.TryParse(countStr, out expectedCount))
                        {
                            result.ExpectedCount = expectedCount;

                            // 验证单值*n格式限制
                            if (expectedCount == 1 && result.ActualCount == 1)
                            {
                                result.IsValid = false;
                                result.ErrorMessage = "单值情况不允许使用*n格式，应使用纯检测项名称";
                                return result;
                            }

                            // 验证数量一致性
                            if (result.ActualCount != expectedCount)
                            {
                                result.IsValid = false;
                                result.ErrorMessage = "测量值数量(" + result.ActualCount + ")与期望数量(" + expectedCount + ")不一致";
                                return result;
                            }
                        }
                    }
                }

                result.IsValid = true;
                return result;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ErrorMessage = "解析测量值失败: " + ex.Message;
                return result;
            }
        }

        /// <summary>
        /// 获取判定级别的优先级（数值越大表示越严重）
        /// </summary>
        /// <param name="judgment">判定结果</param>
        /// <returns>优先级</returns>
        private static int GetJudgmentPriority(string judgment)
        {
            switch (judgment)
            {
                case "OK": return 0;
                case "NG1": return 1;
                case "NG2": return 2;
                case "ERROR": return 3;
                default: return 3; // 未知判定视为ERROR
            }
        }

        /// <summary>
        /// 比较两个判定结果，返回更严重的那个
        /// </summary>
        /// <param name="judgment1">判定结果1</param>
        /// <param name="judgment2">判定结果2</param>
        /// <returns>更严重的判定结果</returns>
        private static string GetWorseJudgment(string judgment1, string judgment2)
        {
            return GetJudgmentPriority(judgment1) >= GetJudgmentPriority(judgment2) ? judgment1 : judgment2;
        }

        /// <summary>
        /// 对单个检测项进行判定
        /// </summary>
        /// <param name="itemName">检测项名称</param>
        /// <param name="measureValueStr">测量值字符串</param>
        /// <param name="configDict">配置字典</param>
        /// <returns>检测项判定结果</returns>
        private static ItemJudgmentResult EvaluateItem(string itemName, string measureValueStr, Dictionary<string, InspecItemConfig> configDict)
        {
            var result = new ItemJudgmentResult { ItemName = itemName };

            try
            {
                DebugLogger.Log("  开始评估检测项: " + itemName);
                DebugLogger.Log("  测量值字符串: " + measureValueStr);

                // 1. 查找配置
                InspecItemConfig config;
                if (!configDict.TryGetValue(itemName, out config))
                {
                    result.Judgment = "ERROR";
                    result.ErrorMessage = DebugLogger.LogError("检测项配置", "配置缺失",
                        "检测项 '" + itemName + "' 在配置文件中不存在，可用配置项: [" +
                        string.Join(", ", configDict.Keys) + "]");
                    return result;
                }
                DebugLogger.Log("  找到配置: " + itemName + " -> 相机" + config.Camera);

                result.Config = config;

                // 2. 验证配置
                DebugLogger.Log("  开始验证配置有效性");
                var configValidation = ValidateConfig(config);
                if (!configValidation.IsValid)
                {
                    result.Judgment = "ERROR";
                    result.ErrorMessage = DebugLogger.LogError("配置验证", "配置无效",
                        "检测项 '" + itemName + "' 的配置验证失败: " + configValidation.ErrorMessage);
                    return result;
                }
                DebugLogger.Log("  配置验证通过");

                // 3. 解析测量值和数量信息
                DebugLogger.Log("  开始解析测量值和数量信息");
                if (string.IsNullOrEmpty(measureValueStr))
                {
                    result.Judgment = "ERROR";
                    result.ErrorMessage = DebugLogger.LogError("测量值解析", "测量值为空",
                        "检测项 '" + itemName + "' 的测量值为空或null");
                    return result;
                }

                // 解析测量值和数量信息
                var parseResult = ParseMeasureValueWithCount(measureValueStr);
                if (!parseResult.IsValid)
                {
                    result.Judgment = "ERROR";
                    result.ErrorMessage = DebugLogger.LogError("测量值解析", "解析失败",
                        "检测项 '" + itemName + "' 的测量值解析失败: " + parseResult.ErrorMessage +
                        " (输入: " + measureValueStr + ")");
                    return result;
                }
                DebugLogger.Log("  测量值解析成功，实际数量: " + parseResult.ActualCount +
                              ", 期望数量: " + parseResult.ExpectedCount);

            // 分割多个测量值
            string[] valueStrings = parseResult.MeasureValues;
            string worstJudgment = "OK"; // 初始化为最好的判定结果
            float? firstValidValue = null;

            foreach (string valueStr in valueStrings)
            {
                string trimmedValue = valueStr.Trim();
                if (string.IsNullOrEmpty(trimmedValue)) continue;

                float measureValue;
                if (!float.TryParse(trimmedValue, out measureValue))
                {
                    // 如果任何一个值无效，直接返回ERROR
                    result.Judgment = "ERROR";
                    result.ErrorMessage = "测量值无效: " + trimmedValue;
                    return result;
                }

                // 记录第一个有效值用于显示
                if (firstValidValue == null)
                {
                    firstValidValue = measureValue;
                }

                // 对当前值进行判定
                string currentJudgment = EvaluateSingleValue(measureValue, config);

                // 取更严重的判定结果
                worstJudgment = GetWorseJudgment(worstJudgment, currentJudgment);

                // 如果已经是最严重的ERROR，可以提前退出
                if (worstJudgment == "ERROR")
                {
                    break;
                }
            }

                result.MeasureValue = firstValidValue;
                result.Judgment = worstJudgment;
                DebugLogger.Log("  检测项 '" + itemName + "' 评估完成，最终判定: " + worstJudgment);
                return result;
            }
            catch (Exception ex)
            {
                result.Judgment = "ERROR";
                result.ErrorMessage = DebugLogger.LogError("检测项评估", "系统异常",
                    "检测项 '" + itemName + "' 评估过程中发生异常: " + ex.Message +
                    " (异常类型: " + ex.GetType().Name + ")");
                return result;
            }
        }

        /// <summary>
        /// 获取最近一次判定的调试信息
        /// </summary>
        /// <returns>调试信息字符串</returns>
        /// <summary>
        /// 获取调试信息
        /// </summary>
        /// <returns>调试日志内容</returns>
        public static string GetDebugInfo()
        {
            return DebugLogger.GetLogs();
        }

        /// <summary>
        /// 清空调试信息
        /// </summary>
        public static void ClearDebugInfo()
        {
            DebugLogger.ClearLogs();
        }

        /// <summary>
        /// 启用或禁用调试模式
        /// </summary>
        /// <param name="enabled">是否启用调试</param>
        public static void SetDebugEnabled(bool enabled)
        {
            DebugLogger.SetDebugEnabled(enabled);
        }

        /// <summary>
        /// 对单个测量值进行判定
        /// </summary>
        /// <param name="measureValue">测量值</param>
        /// <param name="config">配置</param>
        /// <returns>判定结果</returns>
        private static string EvaluateSingleValue(float measureValue, InspecItemConfig config)
        {
            // 应用偏移量调整测量值
            float adjustedValue = measureValue + config.Offset;

            // 执行判定逻辑（使用调整后的测量值）
            if (adjustedValue >= config.GoodLower && adjustedValue <= config.GoodUpper)
            {
                return "OK";
            }
            else if (adjustedValue >= config.LightLower && adjustedValue <= config.LightUpper)
            {
                return "NG1";
            }
            else
            {
                return "NG2";
            }
        }

        /// <summary>
        /// 确定判定分类
        /// </summary>
        /// <param name="itemResults">所有检测项的判定结果</param>
        /// <returns>判定分类</returns>
        private static string DetermineJudgmentCategory(List<ItemJudgmentResult> itemResults)
        {
            // 如果有任意一个检测项为ERROR，则判定分类为异常
            if (itemResults.Any(r => r.Judgment == "ERROR"))
            {
                return "异常";
            }

            // 如果有任意一个检测项为NG2，则判定分类为严重
            if (itemResults.Any(r => r.Judgment == "NG2"))
            {
                return "严重";
            }

            // 如果有任意一个检测项为NG1，则判定分类为轻微
            if (itemResults.Any(r => r.Judgment == "NG1"))
            {
                return "轻微";
            }

            // 如果所有检测项都为OK，则判定分类为良品
            return "良品";
        }

        /// <summary>
        /// 比较两个判定结果的严重程度
        /// </summary>
        private static bool IsWorseResult(string newResult, string currentBest)
        {
            var severity = new Dictionary<string, int>
            {
                { "OK", 0 },
                { "NG1", 1 },
                { "NG2", 2 },
                { "ERROR", 3 }
            };

            return severity[newResult] > severity[currentBest];
        }

        /// <summary>
        /// 确定最终结果，包括判定分类和出料分类
        /// </summary>
        /// <param name="itemResults">所有检测项的判定结果</param>
        /// <returns>出料分类</returns>
        private static string DetermineFinalResult(List<ItemJudgmentResult> itemResults)
        {
            // 异常判定
            var errorItem = itemResults.FirstOrDefault(r => r.Judgment == "ERROR");
            if (errorItem != null)
            {
                return "0"; // 异常分类固定为'0'
            }

            // 严重判定
            var severeItems = itemResults.Where(r => r.Judgment == "NG2").ToList();
            if (severeItems.Any())
            {
                // 如果有多个严重项，可以选择第一个或根据特定逻辑（此处选择第一个）
                return severeItems.First().Config.SevereCategory;
            }

            // 轻微判定
            var lightItems = itemResults.Where(r => r.Judgment == "NG1").ToList();
            if (lightItems.Any())
            {
                // 如果有多个轻微项，选择第一个
                return lightItems.First().Config.LightCategory;
            }

            // 良品判定
            // 只有所有项都为OK时，才判定为良品
            if (itemResults.All(r => r.Judgment == "OK"))
            {
                // 对于良品，任何一个OK项的配置都可以使用，因为良品分类是相同的
                var goodItem = itemResults.FirstOrDefault();
                if (goodItem != null)
                {
                    return goodItem.Config.GoodCategory;
                }
            }

            // 如果混合了OK和未知的（比如空的itemResults），或者其他未处理的组合，默认为异常
            return "0";
        }

        /// <summary>
        /// 验证配置的取值约束
        /// </summary>
        /// <param name="config">检测项配置</param>
        /// <returns>验证结果</returns>
        private static ValidationResult ValidateConfig(InspecItemConfig config)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                // 1. 良品上限 ≥ 良品下限
                if (config.GoodUpper < config.GoodLower)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "良品上限必须大于等于良品下限";
                    return result;
                }

                // 2. 轻微上限 ≥ 良品上限
                if (config.LightUpper < config.GoodUpper)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "轻微上限必须大于等于良品上限";
                    return result;
                }


                // 4. 良品分类、轻微分类、严重分类字段值不允许为空
                if (string.IsNullOrEmpty(config.GoodCategory))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "良品分类字段值不能为空";
                    return result;
                }

                if (string.IsNullOrEmpty(config.LightCategory))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "轻微分类字段值不能为空";
                    return result;
                }

                if (string.IsNullOrEmpty(config.SevereCategory))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "严重分类字段值不能为空";
                    return result;
                }

                // 5. 良品分类字段值不允许与轻微分类或严重分类字段值相同
                if (config.GoodCategory == config.LightCategory)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "良品分类不能与轻微分类相同";
                    return result;
                }

                if (config.GoodCategory == config.SevereCategory)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "良品分类不能与严重分类相同";
                    return result;
                }

                // 6. 良品分类、轻微分类、严重分类字段取值范围是'1'-'9'，字符型
                if (!IsValidCategoryValue(config.GoodCategory))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "良品分类取值必须是'1'-'9'";
                    return result;
                }

                if (!IsValidCategoryValue(config.LightCategory))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "轻微分类取值必须是'1'-'9'";
                    return result;
                }

                if (!IsValidCategoryValue(config.SevereCategory))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "严重分类取值必须是'1'-'9'";
                    return result;
                }

                return result;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ErrorMessage = "配置验证异常: " + ex.Message;
                return result;
            }
        }

        /// <summary>
        /// 验证分类值是否有效（'1'-'9'）
        /// </summary>
        /// <param name="value">分类值</param>
        /// <returns>是否有效</returns>
        private static bool IsValidCategoryValue(string value)
        {
            if (string.IsNullOrEmpty(value) || value.Length != 1)
            {
                return false;
            }
            char c = value[0];
            return c >= '1' && c <= '9';
        }
    }

    /// <summary>
    /// 解析结果
    /// </summary>
    public class ParseResult
    {
        public bool IsValid { get; set; }
        public Dictionary<string, string> Items { get; set; }
    }

    /// <summary>
    /// 测量值解析结果
    /// </summary>
    public class MeasureValueParseResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        public string[] MeasureValues { get; set; }
        public int ActualCount { get; set; }
        public int ExpectedCount { get; set; }
    }

    /// <summary>
    /// 单个检测项的判定结果
    /// </summary>
    public class ItemJudgmentResult
    {
        public string ItemName { get; set; }
        public string Judgment { get; set; }
        public float? MeasureValue { get; set; }
        public InspecItemConfig Config { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
    }
}
