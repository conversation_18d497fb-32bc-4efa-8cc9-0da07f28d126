﻿using System;
using System.Text;
using System.Windows.Forms;
using Script.Methods;
using System.Xml;
/************************************
Shell Module default code: using .NET Framwwork 4.6.1
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;  
	string xmlFilePath;
	
    
    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;
        xmlFilePath = @"E:\test\test1.xml";
      
       
    }
    
    public string CurrentParamName;
	public float CurrentUpperLimit;

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
    	

    	c = 4;
		name = "asd";
	       
	            XmlDocument xmlDoc = new XmlDocument();
	            xmlDoc.Load(xmlFilePath);
	
	            XmlNodeList nodes = xmlDoc.SelectNodes("/Parameters/Parameter");
	            foreach (XmlNode node in nodes)
	            {
	                string Name = node.SelectSingleNode("Name").InnerText;
	                float upper = float.Parse(node.SelectSingleNode("UpperLimit").InnerText);
	                float lower = float.Parse(node.SelectSingleNode("LowerLimit").InnerText);
	
	                CurrentParamName = Name;
					CurrentUpperLimit = upper;
	                
	                // 改用 string.Format 或字符串拼接
	                Console.WriteLine(string.Format("参数: {0}, 范围: {1}~{2}", Name, lower, upper));
	                
	                // 假设通过全局变量传递（按实际 API 调整）
	                //SetGV(name + "_Max", upper);  // 旧版语法
	                //SetGV(name + "_Min", lower);
	            }
	            return true;
	       

	    }
        
    
}
                            