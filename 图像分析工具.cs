// 图像分析工具 - 用于分析存储的产品图像，查找潜在漏检
using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using System.Text;

public class ImageAnalysisTool
{
    private const string LOG_PATH = @"D:\HODD\DetectionLog.txt";
    private const string ANALYSIS_RESULT_PATH = @"D:\HODD\AnalysisResult.txt";
    private const double SIMILARITY_THRESHOLD = 2.0;
    
    public class DetectionRecord
    {
        public DateTime DetectionTime { get; set; }
        public string ProductId { get; set; }
        public string Result { get; set; }
        public double MaxDis { get; set; }
        public double[] DisValues { get; set; }
        public string ImagePath { get; set; }
    }
    
    // 分析所有检测记录，查找潜在漏检
    public void AnalyzeDetectionHistory()
    {
        try
        {
            Console.WriteLine("开始分析检测历史记录...");
            
            // 读取检测日志
            var records = LoadDetectionRecords();
            Console.WriteLine($"加载了 {records.Count} 条检测记录");
            
            // 分离OK和NG产品
            var okProducts = records.Where(r => r.Result == "OK").ToList();
            var ngProducts = records.Where(r => r.Result == "NG").ToList();
            
            Console.WriteLine($"OK产品: {okProducts.Count} 个");
            Console.WriteLine($"NG产品: {ngProducts.Count} 个");
            
            // 查找疑似漏检
            var suspiciousProducts = FindSuspiciousProducts(okProducts, ngProducts);
            
            // 生成分析报告
            GenerateAnalysisReport(suspiciousProducts, okProducts.Count, ngProducts.Count);
            
            Console.WriteLine($"分析完成，发现 {suspiciousProducts.Count} 个疑似漏检产品");
            Console.WriteLine($"详细报告已保存到: {ANALYSIS_RESULT_PATH}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"分析过程出错: {ex.Message}");
        }
    }
    
    // 加载检测记录
    private List<DetectionRecord> LoadDetectionRecords()
    {
        var records = new List<DetectionRecord>();
        
        if (!File.Exists(LOG_PATH))
        {
            Console.WriteLine($"日志文件不存在: {LOG_PATH}");
            return records;
        }
        
        var lines = File.ReadAllLines(LOG_PATH);
        
        foreach (var line in lines)
        {
            try
            {
                var parts = line.Split(',');
                if (parts.Length >= 6)
                {
                    var record = new DetectionRecord
                    {
                        DetectionTime = DateTime.Parse(parts[0]),
                        ProductId = parts[1],
                        Result = parts[2],
                        MaxDis = double.Parse(parts[3]),
                        DisValues = parts[4].Split(';').Select(double.Parse).ToArray(),
                        ImagePath = parts[5]
                    };
                    records.Add(record);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析日志行失败: {line}, 错误: {ex.Message}");
            }
        }
        
        return records.OrderBy(r => r.DetectionTime).ToList();
    }
    
    // 查找疑似漏检产品
    private List<SuspiciousProduct> FindSuspiciousProducts(List<DetectionRecord> okProducts, List<DetectionRecord> ngProducts)
    {
        var suspiciousProducts = new List<SuspiciousProduct>();
        
        Console.WriteLine("正在分析产品相似性...");
        
        foreach (var okProduct in okProducts)
        {
            // 查找与此OK产品相似的NG产品
            var similarNGProducts = new List<(DetectionRecord ng, double similarity)>();
            
            foreach (var ngProduct in ngProducts)
            {
                double similarity = CalculateDisSimilarity(okProduct.DisValues, ngProduct.DisValues);
                
                if (similarity < SIMILARITY_THRESHOLD)
                {
                    similarNGProducts.Add((ngProduct, similarity));
                }
            }
            
            if (similarNGProducts.Any())
            {
                // 找到最相似的NG产品
                var mostSimilar = similarNGProducts.OrderBy(x => x.similarity).First();
                
                suspiciousProducts.Add(new SuspiciousProduct
                {
                    OKProduct = okProduct,
                    SimilarNGProduct = mostSimilar.ng,
                    Similarity = mostSimilar.similarity,
                    DisThresholdExceeded = mostSimilar.ng.MaxDis,
                    SuspicionLevel = CalculateSuspicionLevel(mostSimilar.similarity, okProduct.MaxDis, mostSimilar.ng.MaxDis)
                });
            }
        }
        
        return suspiciousProducts.OrderBy(s => s.Similarity).ToList();
    }
    
    // 计算dis参数相似度
    private double CalculateDisSimilarity(double[] dis1, double[] dis2)
    {
        if (dis1.Length != dis2.Length)
            return double.MaxValue;
        
        double sumSquaredDiff = 0;
        for (int i = 0; i < dis1.Length; i++)
        {
            double diff = dis1[i] - dis2[i];
            sumSquaredDiff += diff * diff;
        }
        
        return Math.Sqrt(sumSquaredDiff / dis1.Length);
    }
    
    // 计算疑似程度
    private string CalculateSuspicionLevel(double similarity, double okMaxDis, double ngMaxDis)
    {
        double disRatio = okMaxDis / ngMaxDis;
        
        if (similarity < 0.5 && disRatio > 0.95)
            return "高度疑似";
        else if (similarity < 1.0 && disRatio > 0.90)
            return "中度疑似";
        else
            return "低度疑似";
    }
    
    // 生成分析报告
    private void GenerateAnalysisReport(List<SuspiciousProduct> suspiciousProducts, int totalOK, int totalNG)
    {
        var report = new StringBuilder();
        
        report.AppendLine("=== 产品检测漏检分析报告 ===");
        report.AppendLine($"分析时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        report.AppendLine($"总检测产品数: {totalOK + totalNG}");
        report.AppendLine($"OK产品数: {totalOK}");
        report.AppendLine($"NG产品数: {totalNG}");
        report.AppendLine($"疑似漏检产品数: {suspiciousProducts.Count}");
        report.AppendLine($"漏检率估算: {(double)suspiciousProducts.Count / totalOK * 100:F2}%");
        report.AppendLine();
        
        if (suspiciousProducts.Any())
        {
            report.AppendLine("=== 疑似漏检产品详情 ===");
            report.AppendLine("序号\t疑似程度\t相似度\tOK产品ID\t\tNG产品ID\t\tOK最大dis\tNG最大dis\tOK图像路径\t\t\t\tNG图像路径");
            
            for (int i = 0; i < suspiciousProducts.Count; i++)
            {
                var sp = suspiciousProducts[i];
                report.AppendLine($"{i + 1}\t{sp.SuspicionLevel}\t{sp.Similarity:F3}\t" +
                               $"{sp.OKProduct.ProductId}\t{sp.SimilarNGProduct.ProductId}\t" +
                               $"{sp.OKProduct.MaxDis:F3}\t\t{sp.SimilarNGProduct.MaxDis:F3}\t\t" +
                               $"{sp.OKProduct.ImagePath}\t{sp.SimilarNGProduct.ImagePath}");
            }
            
            report.AppendLine();
            report.AppendLine("=== 建议处理措施 ===");
            report.AppendLine("1. 检查高度疑似的产品图像，确认是否为漏检");
            report.AppendLine("2. 调整检测参数或阈值设置");
            report.AppendLine("3. 优化图像采集条件（光照、角度等）");
            report.AppendLine("4. 考虑增加多次检测或复检机制");
        }
        else
        {
            report.AppendLine("未发现疑似漏检产品。");
        }
        
        File.WriteAllText(ANALYSIS_RESULT_PATH, report.ToString(), Encoding.UTF8);
    }
    
    // 按日期分析
    public void AnalyzeByDate(DateTime startDate, DateTime endDate)
    {
        Console.WriteLine($"分析时间段: {startDate:yyyy-MM-dd} 到 {endDate:yyyy-MM-dd}");
        
        var allRecords = LoadDetectionRecords();
        var filteredRecords = allRecords.Where(r => r.DetectionTime >= startDate && r.DetectionTime <= endDate).ToList();
        
        Console.WriteLine($"时间段内检测记录: {filteredRecords.Count} 条");
        
        var okProducts = filteredRecords.Where(r => r.Result == "OK").ToList();
        var ngProducts = filteredRecords.Where(r => r.Result == "NG").ToList();
        
        var suspiciousProducts = FindSuspiciousProducts(okProducts, ngProducts);
        
        string dateRangeReport = ANALYSIS_RESULT_PATH.Replace(".txt", $"_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}.txt");
        GenerateAnalysisReport(suspiciousProducts, okProducts.Count, ngProducts.Count);
        
        Console.WriteLine($"时间段分析完成，报告保存到: {dateRangeReport}");
    }
    
    public class SuspiciousProduct
    {
        public DetectionRecord OKProduct { get; set; }
        public DetectionRecord SimilarNGProduct { get; set; }
        public double Similarity { get; set; }
        public double DisThresholdExceeded { get; set; }
        public string SuspicionLevel { get; set; }
    }
}

// 主程序入口
public class Program
{
    public static void Main(string[] args)
    {
        var analyzer = new ImageAnalysisTool();
        
        Console.WriteLine("=== 产品检测漏检分析工具 ===");
        Console.WriteLine("1. 分析全部历史记录");
        Console.WriteLine("2. 分析指定日期范围");
        Console.Write("请选择分析模式 (1/2): ");
        
        string choice = Console.ReadLine();
        
        if (choice == "1")
        {
            analyzer.AnalyzeDetectionHistory();
        }
        else if (choice == "2")
        {
            Console.Write("请输入开始日期 (yyyy-MM-dd): ");
            DateTime startDate = DateTime.Parse(Console.ReadLine());
            
            Console.Write("请输入结束日期 (yyyy-MM-dd): ");
            DateTime endDate = DateTime.Parse(Console.ReadLine());
            
            analyzer.AnalyzeByDate(startDate, endDate);
        }
        else
        {
            Console.WriteLine("无效选择，执行全部历史记录分析...");
            analyzer.AnalyzeDetectionHistory();
        }
        
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}
