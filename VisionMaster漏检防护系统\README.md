# VisionMaster 4.3.0 漏检防护系统

## 📋 系统概述

本系统专门针对圆形产品钩角检测的漏检问题，通过自动存图、特征匹配和相似性分析来识别潜在的漏检产品。

## 📁 文件结构

```
VisionMaster漏检防护系统/
├── README.md                           # 本文件 - 系统说明
├── 核心代码/
│   ├── VisionMaster_检测存图脚本.cs    # VisionMaster主检测脚本
│   └── 图像分析工具.cs                 # 离线分析工具
├── 部署工具/
│   ├── 部署漏检防护系统.bat            # 一键部署脚本
│   ├── 精准定位漏检产品.bat            # 🎯 精准定位工具
│   └── 编译分析工具.bat                # 编译工具（兼容性优化）
├── 配置文档/
│   └── VisionMaster漏检防护配置说明.md # 详细配置说明
└── 示例数据/
    └── 测试数据示例.txt                # 测试用的dis和dangle数据
```

## 🎯 核心功能

### 1. **自动存图功能**
- 每次检测自动保存产品图像（BMP格式）
- 按日期分类存储到 `D:\HODD\ProductImages`
- 文件命名：`产品ID_检测结果.bmp`

### 2. **实时漏检监控**
- 基于dis参数进行相似性分析
- 当OK产品与近期NG产品相似度过高时触发警报
- 实时记录检测日志和警报信息

### 3. **🎯 智能精准定位**
- **一键精准定位**：无需手动逐个查找，自动找到最可疑的漏检产品
- **智能排序**：按相似度自动排序，优先显示最可能的漏检
- **自动图像对比**：生成图像对比脚本，快速验证疑似漏检
- **详细参数分析**：提供dis参数逐个对比分析

### 4. **离线分析工具**
- 分析历史检测数据，查找疑似漏检
- 生成详细的漏检分析报告
- 支持按时间段筛选分析

## 🚀 快速开始

### 🎯 精准定位漏检产品（推荐）
```bash
# 一键精准定位，无需手动查找
双击：部署工具/精准定位漏检产品.bat
```
**功能**：自动分析所有历史数据，精准定位最可疑的漏检产品，按相似度排序显示。

### 1. 部署系统
```bash
# 运行部署脚本
双击：部署工具/部署漏检防护系统.bat
```

### 2. 集成VisionMaster
将 `核心代码/VisionMaster_检测存图脚本.cs` 中的代码集成到您的VisionMaster项目中。

### 3. 配置参数
根据 `配置文档/VisionMaster漏检防护配置说明.md` 调整检测参数。

### 4. 测试验证
使用 `示例数据/测试数据示例.txt` 中的数据进行功能测试。

## ⚙️ 关键参数

```csharp
// 检测阈值（根据您的实际阈值调整）
private const double DIS_THRESHOLD = 385.5;

// 相似度阈值（值越小检测越敏感）
private const double SIMILARITY_THRESHOLD = 2.0;

// 存储路径（符合用户偏好）
private const string SAVE_PATH = @"D:\HODD\ProductImages";
```

## 🔍 漏检识别原理

### 问题背景
您提到的情况：3000个料中漏了一个没判断出来，但重复过这个料时dis又超过了阈值能判断出来。

### 解决方案
1. **实时监控**：每个OK产品与近期NG产品进行dis参数相似性比较
2. **相似度计算**：使用欧几里得距离计算dis数组的相似性
3. **智能判定**：结合相似度和dis数值比例进行疑似程度分级

### 识别逻辑
- 当OK产品的dis参数与某个NG产品的dis参数非常相似时
- 系统会判定这个OK产品可能是漏检
- 触发警报并记录到日志中

## 📊 使用效果

### 🎯 精准定位优势
- **无需手动查找**：系统自动分析3000个产品，精准定位漏检
- **智能排序**：按相似度排序，最可疑的产品排在最前面
- **一键对比**：自动生成图像对比脚本，快速验证
- **详细分析**：提供dis参数逐个对比，精确到每个钩角

### 传统功能
- **预防漏检**：实时发现疑似漏检产品
- **历史分析**：找出所有可能的历史漏检
- **图像证据**：保存图像用于人工确认
- **数据追溯**：完整的检测日志便于问题分析

## 🛠️ 技术特点

- **符合用户偏好**：使用D:\HODD目录、BMP格式、详细日志
- **工业级可靠**：异常处理完善、性能影响最小
- **易于维护**：一键部署、详细文档、管理工具齐全
- **兼容性强**：修复编译错误，支持.NET Framework 4.0+

## 📞 技术支持

如需进一步定制或遇到技术问题，请参考：
1. `配置文档/VisionMaster漏检防护配置说明.md` - 详细配置指南
2. 核心代码中的注释说明
3. 部署脚本生成的日志文件

---

**注意**：本系统基于您提供的dis参数特征进行漏检识别，dangle参数因位置变化较大暂不用于匹配。系统已针对您的使用偏好进行优化配置。
