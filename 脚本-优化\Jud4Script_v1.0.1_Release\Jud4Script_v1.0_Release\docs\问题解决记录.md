# Jud4Script.dll 开发问题解决记录

## 项目概述
基于VisionMaster的C#动态库开发，实现检测项目判定功能。

## 主要问题及解决方案

### 1. Newtonsoft.Json依赖问题
**问题描述：**
```
判定过程发生异常: 未能加载文件或程序集'Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed'或它的某一个依赖项。系统找不到指定的文件。
```

**原因分析：**
- VisionMaster使用特定版本的Newtonsoft.Json (********)
- 项目目录中缺少对应版本的依赖库

**解决方案：**
1. 从VisionMaster安装目录复制正确版本的Newtonsoft.Json.dll
   ```
   源路径：C:\Program Files\VisionMaster4.3.0\Applications\Module(sp)\x64\Logic\ShellModule\DLL\Newtonsoft.Json.dll
   目标路径：项目目录\Newtonsoft.Json.dll
   ```
2. 确保Newtonsoft.Json.dll与Jud4Script.dll在同一目录

**验证方法：**
```powershell
[System.Reflection.Assembly]::LoadFrom("Newtonsoft.Json.dll").FullName
```

### 2. JSON配置文件编码问题
**问题描述：**
```
解析JSON配置文件时出错: Invalid character after parsing property name. Expected ':' but got: 澶. Path '[0]', line 3, position 15.
```

**原因分析：**
- 配置文件保存时使用了错误的编码格式
- 中文字符出现乱码，导致JSON解析失败

**解决方案：**
1. 使用.NET的File.WriteAllText方法以UTF-8编码重新创建配置文件
   ```csharp
   File.WriteAllText(@"D:\LvConfig\产品型号\Sample00.json", jsonContent, Encoding.UTF8);
   ```
2. 确保所有配置文件都使用UTF-8编码保存

**预防措施：**
- 在文档中明确说明配置文件编码要求
- 提供配置文件创建工具或模板

### 3. 配置验证逻辑问题
**问题描述：**
```
配置验证失败: 轻微下限必须小于等于良品下限
```

**原因分析：**
- 配置文件中的数值设置违反了设计约束
- 轻微下限 > 良品下限，不符合验证规则

**问题配置示例：**
```json
{
  "良品上限": 200.0,
  "良品下限": 0.0,
  "轻微下限": 200.0  // 错误：200.0 > 0.0
}
```

**解决方案：**
修正配置文件，确保满足所有约束条件：
```json
{
  "良品上限": 200.0,
  "良品下限": 0.0,
  "轻微下限": 0.0  // 正确：0.0 <= 0.0
}
```

**约束规则：**
- 良品上限 ≥ 良品下限
- 轻微上限 ≥ 良品上限
- 轻微下限 ≤ 良品下限

### 4. 调试和测试过程
**调试策略：**
1. 添加详细的调试输出信息
2. 分步验证每个功能模块
3. 创建简化的测试程序

**关键调试信息：**
```csharp
// 配置加载状态
System.Console.WriteLine("配置项数量: " + config.Count);

// 单项判定结果
System.Console.WriteLine(string.Format("检测项: {0}, 判定: {1}, 测量值: {2}", 
    item.ItemName, item.Judgment, item.MeasureValue));

// 最终判定结果
System.Console.WriteLine("判定分类: " + judgmentCategory + ", 出料分类: " + outputCategory);
```

## 测试结果验证

### 最终测试用例
1. **正常情况**：`Items-3;外圆面伤V180.0;外圆纹路V0.0;槽内多铜V0.0` → 返回 `"1"`
2. **检测项个数不匹配**：`Items-3;外圆面伤V180.0;外圆纹路V0.0` → 返回 `"0"`
3. **测量值无效**：`Items-2;外圆面伤V;外圆纹路V0.0` → 返回 `"0"`
4. **超出良品范围**：`Items-3;外圆面伤V300.0;外圆纹路V0.0;槽内多铜V0.0` → 返回 `"3"`

### 性能和稳定性
- 配置文件缓存机制正常工作
- 线程安全验证通过
- 异常处理机制完善
- 内存使用稳定

## 经验总结

### 开发要点
1. **依赖管理**：确保使用与目标环境兼容的依赖库版本
2. **编码处理**：明确文件编码要求，特别是包含中文的配置文件
3. **配置验证**：实现完善的配置验证机制，提供清晰的错误信息
4. **调试策略**：分阶段调试，从简单到复杂逐步验证功能

### 部署注意事项
1. 确保所有依赖库与主程序在同一目录
2. 配置文件必须使用正确的编码格式
3. 配置数值必须满足所有约束条件
4. 提供完整的测试验证程序

### 维护建议
1. 定期验证配置文件的有效性
2. 监控依赖库版本兼容性
3. 保持测试用例的完整性和时效性
4. 建立配置文件的版本管理机制
