﻿using System;
using System.Text;
using System.Windows.Forms;
using Script.Methods;
/************************************
Shell Module default code: using .NET Framwwork 4.6.1
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;  

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;
       
    }

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
        string imagePath = @"E:\夸克Downloads\20250421\image.bmp";
    
    if (System.IO.File.Exists(imagePath))
    {
        try
        {
            // 使用 System.Drawing.Bitmap 加载图像
            Bitmap bitmap = new Bitmap(imagePath);
            
            // 将 Bitmap 转换为 byte[]（BGR格式）
            Rectangle rect = new Rectangle(0, 0, bitmap.Width, bitmap.Height);
            BitmapData bmpData = bitmap.LockBits(rect, ImageLockMode.ReadOnly, PixelFormat.Format24bppRgb);
            
            int bytes = Math.Abs(bmpData.Stride) * bitmap.Height;
            byte[] rgbValues = new byte[bytes];
            System.Runtime.InteropServices.Marshal.Copy(bmpData.Scan0, rgbValues, 0, bytes);
            bitmap.UnlockBits(bmpData);
            
            // 创建 ImageData 并设置数据
            ImageData imagedata = new ImageData();
            imagedata.SetSize(bitmap.Width, bitmap.Height, 3); // 3通道（BGR）
            imagedata.SetData(rgbValues); // 假设 ImageData 有 SetData 方法
            
            // 输出到 out6
            SetImageValue("out6", imagedata);
        }
        catch (Exception ex)
        {
            MessageBox.Show("加载图像失败: " + ex.Message);
            return false;
        }
    }
    else
    {
        MessageBox.Show("文件不存在: " + imagePath);
        return false;
    }
    
    return true;
    }
}
                            