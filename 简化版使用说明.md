# Jud4Script 简化版使用说明

## 📋 核心功能

代码已简化为最核心的功能：
1. 将 `in0` 参数转换为 `inspecInfo` 格式
2. 调用 `JudgmentEngine.Evaluate(modelNo, inspecInfo)`
3. 将结果输出到 `out0`

## 🔧 代码结构

```csharp
public bool Process()
{
    // 将in0参数转换为inspecInfo格式
    string inspecInfo = ConvertIn0ToInspecInfo(in0);
    
    // 调用判定引擎
    string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);
    
    // 输出结果
    out0 = result;
    
    return true;
}
```

## 🎯 转换逻辑

### 输入格式支持

#### 1. 键值对格式
```
in0 = "外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000"
↓
inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"
```

#### 2. 逗号分隔格式
```
in0 = "1.250,0.500,0.000"
↓
inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"
```

#### 3. 分号分隔格式
```
in0 = "1.250;0.500;0.000"
↓
inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"
```

#### 4. 单个数值
```
in0 = "1.250"
↓
inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆碰伤=1.250;"
```

## 📊 缺陷项目映射

数组格式按顺序对应以下缺陷项目：

| 位置 | 缺陷项目 |
|------|---------|
| 1 | 外圆碰伤 |
| 2 | 外圆纹路 |
| 3 | 槽内多铜 |
| 4 | 槽深异常 |
| 5 | 槽宽异常 |
| 6 | 表面划伤 |
| 7 | 尺寸偏差 |
| 8 | 形状变形 |

**修改方法**：在 `GetDefectItemNames()` 函数中调整缺陷项目名称。

## 🚀 使用示例

### VisionMaster中的调用

```csharp
// 设置输入参数
modelNo = "0523A-F";
in0 = "外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000";

// 调用Process()函数
// 系统自动转换并调用JudgmentEngine.Evaluate()

// 获取结果
string result = out0; // "1", "3", "4" 等
```

### 转换过程示例

```csharp
// 输入
modelNo = "0523A-F"
in0 = "1.250,0.500,0.000"

// 转换后的inspecInfo
inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"

// 调用
JudgmentEngine.Evaluate("0523A-F", inspecInfo)

// 输出
out0 = "3" // 假设判定结果为轻微缺陷
```

## ⚙️ 关键说明

1. **C1**: 代表相机1(CAM1)，固定前缀
2. **modelNo**: 直接传给JudgmentEngine.Evaluate()
3. **in0**: 转换为inspecInfo格式后传给JudgmentEngine.Evaluate()
4. **out0**: 接收JudgmentEngine.Evaluate()的返回结果

## 🔧 自定义修改

### 修改缺陷项目名称

在 `GetDefectItemNames()` 函数中修改：

```csharp
private string[] GetDefectItemNames()
{
    return new string[]
    {
        "你的缺陷项目1",
        "你的缺陷项目2",
        "你的缺陷项目3",
        // ... 更多项目
    };
}
```

### 修改相机编号

如果需要修改相机编号，在 `ConvertIn0ToInspecInfo()` 函数中修改：

```csharp
// 将 C1 改为其他相机编号
inspecInfoBuilder.Append("CAM=C2;TS=;JD=;;Items=;");
```

## ✅ 完成

代码已简化为最核心的功能，只做两件事：
1. 转换 `in0` 为 `inspecInfo` 格式
2. 调用 `JudgmentEngine.Evaluate(modelNo, inspecInfo)` 并输出结果

无多余的日志、异常处理等功能，专注于核心转换和调用逻辑。
