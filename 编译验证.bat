@echo off
chcp 65001 >nul
echo ========================================
echo Jud4Script 代码编译验证
echo ========================================
echo.

echo 正在检查编译器...
where csc >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到C#编译器
    echo    请安装.NET Framework SDK或.NET SDK
    echo.
    pause
    exit /b 1
)

echo ✓ 找到C#编译器
echo.

echo 正在编译配置-Jud4Script.cs...
echo.

:: 编译主脚本文件
csc /target:library /out:配置-Jud4Script.dll 配置-Jud4Script.cs

if %errorlevel% equ 0 (
    echo ✅ 编译成功！
    echo 生成文件: 配置-Jud4Script.dll
    echo.
    
    echo 代码验证通过，可以在VisionMaster中使用。
    echo.
    
    :: 清理生成的文件
    if exist "配置-Jud4Script.dll" del "配置-Jud4Script.dll"
    echo 已清理临时文件。
) else (
    echo ❌ 编译失败！
    echo.
    echo 请检查代码中是否还有语法错误。
)

echo.
echo 正在编译测试程序...
echo.

:: 编译测试程序
csc /target:exe /out:Jud4Script转换测试.exe Jud4Script转换测试.cs

if %errorlevel% equ 0 (
    echo ✅ 测试程序编译成功！
    echo.
    
    echo 是否运行测试程序？(Y/N)
    set /p runtest=
    if /i "%runtest%"=="Y" (
        echo.
        echo 正在运行测试程序...
        echo.
        Jud4Script转换测试.exe
    )
    
    :: 清理生成的文件
    if exist "Jud4Script转换测试.exe" del "Jud4Script转换测试.exe"
    echo 已清理测试程序文件。
) else (
    echo ❌ 测试程序编译失败！
)

echo.
echo ========================================
echo 验证完成
echo ========================================
echo.
pause
