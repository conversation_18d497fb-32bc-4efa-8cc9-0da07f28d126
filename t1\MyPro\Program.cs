/*
using System;
using System.Linq;
using System.Text;

int number = 42;
string message = "The answer to life, the universe, and everything is ";

Console.WriteLine($"{message}{number}.");

var info = string.Format("number: {0},message: {1}",number,message);
Console.WriteLine(info);

var sb = new StringBuilder();
sb.Append("Player: ").Append(number).Append(",").Append("Message: ").Append(message);
Console.WriteLine(sb.ToString());

int Add(int a,int b) => a + b;
Console.WriteLine($"Sum of 1 and 2 is {Add(1,2)}.");

var numbers = new[] { 1, 2, 3, 4, 5 };
var evens = numbers.Where(n => n % 2 == 0).ToArray();
Console.WriteLine("Even number: " + string.Join(", ",evens));

try
{
    int zero = 0;
    int result = number/zero;
}
catch (DivideByZeroException ex)
{
    Console.WriteLine("Error: " + ex.Message);
}
catch (Exception ex)
{
    Console.WriteLine("Other Error: " + ex.Message);
}
finally
{
    Console.WriteLine("清理资源...");
}
*/