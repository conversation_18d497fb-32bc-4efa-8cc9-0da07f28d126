using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.IO;
using System.Linq;
using Newtonsoft.Json;

namespace Jud4Script
{
    /// <summary>
    /// 静态类，用于管理和缓存检测配置。
    /// 实现了线程安全的懒加载和高效查找，确保每个配置文件只被读取和解析一次。
    /// </summary>
    public static class ConfigManager
    {
        // 使用ConcurrentDictionary实现线程安全的缓存。
        // 键是 modelNo (产品型号)，值是该型号对应的配置字典。
        private static readonly ConcurrentDictionary<string, Dictionary<string, InspecItemConfig>> _cache =
            new ConcurrentDictionary<string, Dictionary<string, InspecItemConfig>>();

        /// <summary>
        /// 根据产品型号获取其所有检测项的配置字典。
        /// 此方法是线程安全的。
        /// </summary>
        /// <param name="modelNo">产品型号</param>
        /// <returns>配置字典</returns>
        public static Dictionary<string, InspecItemConfig> GetModelConfig(string modelNo)
        {
            // GetOrAdd确保了LoadConfig(modelNo)对于每个modelNo只会被执行一次。
            // 如果缓存中已存在该modelNo的键，则直接返回其值。
            // 如果不存在，则调用LoadConfig(modelNo)来创建值，存入缓存后再返回。
            // 整个过程是原子性的，避免了竞态条件。
            return _cache.GetOrAdd(modelNo, LoadConfig);
        }

        /// <summary>
        /// 加载并解析指定型号的JSON配置文件。
        /// </summary>
        /// <param name="modelNo">产品型号</param>
        /// <returns>配置字典</returns>
        private static Dictionary<string, InspecItemConfig> LoadConfig(string modelNo)
        {
            string jsonPath = string.Format(@"D:\LvConfig\产品型号\{0}.json", modelNo);

            // 如果D盘路径不存在，尝试从当前目录读取（用于测试）
            if (!File.Exists(jsonPath))
            {
                string currentDirPath = string.Format("{0}.json", modelNo);
                if (File.Exists(currentDirPath))
                {
                    jsonPath = currentDirPath;
                    Console.WriteLine("从当前目录加载配置文件：" + jsonPath);
                }
                else
                {
                    Console.WriteLine("配置文件不存在：" + jsonPath);
                    Console.WriteLine("也未在当前目录找到：" + currentDirPath);
                    // 返回一个空字典，避免空引用异常
                    return new Dictionary<string, InspecItemConfig>();
                }
            }

            try
            {
                string jsonContent = File.ReadAllText(jsonPath);
                
                var modelConfig = JsonConvert.DeserializeObject<ModelConfig>(jsonContent);

                if (modelConfig == null || modelConfig.ItemList == null)
                {
                    Console.WriteLine("JSON配置文件格式不正确或缺少'检测项目列表'：" + jsonPath);
                    return new Dictionary<string, InspecItemConfig>();
                }

                // 将列表转换为字典，以检测项目名称为键，实现O(1)复杂度的快速查找
                return modelConfig.ItemList.ToDictionary(c => c.ItemName, c => c);
            }
            catch (Exception ex)
            {
                Console.WriteLine(string.Format("解析JSON配置文件'{0}'时出错: {1}", jsonPath, ex.Message));
                return new Dictionary<string, InspecItemConfig>();
            }
        }
    }

    public class ModelConfig
    {
        [JsonProperty("检测项目列表")]
        public List<InspecItemConfig> ItemList { get; set; }
    }
}
