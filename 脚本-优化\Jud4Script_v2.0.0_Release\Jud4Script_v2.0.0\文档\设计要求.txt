软件特征：
1，使用C#实现动态库，在VisionMaster的脚本内调用，实现判定功能
2，在C#脚本内调用时，可以使用关键词Using进行声明来引用所述动态库
3，动态库名称为Jud4Script.dll

流程和判定逻辑：
1，配置信息使用的json文件的字段依次如下。
    1）"检测项目"
    2）"分属相机"
    3）"严重分类"
    4）"轻微分类"
    5）"良品分类"
    6）"轻微上限"
    7）"良品上限"
    8）"良品下限"
    9）"轻微下限"
    10）"偏移量"
    11）"预处理模式"（预留字段，用于未来功能扩展）
2，判定流程。
    1）首先对测量值应用偏移量：调整后测量值 = 原始测量值 + 偏移量
    2）如果调整后测量值在良品范围内（良品下限 ≤ 调整后测量值 ≤ 良品上限），则判定为OK
    3）如果调整后测量值在轻微范围内（轻微下限 ≤ 调整后测量值 ≤ 轻微上限）但不在良品范围内，则判定为NG1
    4）如果调整后测量值超出轻微范围，则判定为NG2
    5）如果配置文件中没有该检测项的配置，则判定为ERROR
    6）如果测量值为空或者无法转换为数值类型，则判定为ERROR
3，判定分类。
    1）如果所有检测项都为OK，则判定分类为良品
    2）如果有任意一个检测项为NG1，则判定分类为轻微
    3）如果有任意一个检测项为NG2，则判定分类为严重
    4）如果有任意一个检测项为ERROR，则判定分类为异常
4，出料分类。
    1）如果判定分类为良品，则出料分类取良品分类字段的值
    2）如果判定分类为轻微，则出料分类为轻微分类字段的值
    3）如果判定分类为严重，则出料分类为严重分类字段的值
    4）如果判定分类为异常，则出料分类为异常分类。异常分类固定为'0'
5，取值约束。
    1）良品上限 ≥ 良品下限
    2）轻微上限 ≥ 良品上限
    3）轻微下限 ≤ 良品下限
    4）良品分类字段值不允许与轻微分类或严重分类字段值相同
    5）良品分类、轻微分类、严重分类字段值不允许为空
    6）良品分类、轻微分类、严重分类字段取值范围是'1'-'9'，字符型，表示出料分类的出口位置

程序接口：
1，输入参数 modelNo，其值为产品型号，字符串型，用于在指定目录下查找对应的配置文件
    1）配置文件路径：D:\LvConfig\产品型号\{modelNo}.json
    2）配置文件格式：JSON格式文件
    3）检测项和限度信息，在字段"检测项目列表"中
2，输入参数 inspecInfo，其定义如下
    1）以分号分隔的字符串，例如"CAM=C5;TS=;JD=;;Items=;外圆面伤=180.0;槽宽*3=0.32,0.31,0.35;槽内多铜=0.0;IS4=0;IS5V=;IS6V="
    2）检测结果以"Items="字段值开头，后面跟着检测项及对应数值，不同检测项之间用分号分隔
    3）每个检测项的格式为"检测项目名称=测量值"，例如"外圆面伤=180.0"，表示检测项目为"外圆面伤"，测量值为180.0
    4）检测项的顺序可以任意，不需要与配置文件中的顺序一致
    5）检测项的名称必须与配置文件中的"检测项目"字段完全一致，包括大小写和空格
    6）测量值可以包含小数点，例如"180.0"，也可以是整数，例如"180"
    7）测量值允许为空字符串，例如""，表示没有测量值，若在配置文件中找到对应项的配置参数，则判定结果为ERROR
    8）测量值可以包含正负号，例如"+180"或"-180"
    9）测量值可以包含多个值，例如"槽宽*3=0.32,0.31,0.35"，检测项目名称后跟"*n"，表示该检测项目具有n个测量值，判定结果取最差的那个值
    10）对于测量值具有多值的情形，如果测量值的数量与检测项目名称后的数字n不一致，则判定结果为ERROR
    11）对于单值情形，检测项名称不允许跟随"*n"格式，例如"外圆面伤*1=180.0"是不允许的
    12）对于单值情形，检测项不允许对应多个测量值，例如"外圆面伤=185,179"是不允许的
    13）对于不允许的输入格式，判定结果为ERROR
3，输出参数 result，其值为出料分类，字符型

部署说明：
1，将Jud4Script.dll和Newtonsoft.Json.dll放在同一目录下
2，确保配置文件使用UTF-8编码保存
3，配置文件中的数值约束必须满足设计要求中的取值约束
4，在VisionMaster脚本中使用"using Jud4Script;"引用动态库
5，调用方法：JudgmentEngine.Evaluate(modelNo, inspecInfo)

测试要求：
1，测试用例：检测信息_2025-07-09_02-10-34.txt，产品型号是"0523A-F"
2，配置文件："D:\LvConfig\产品型号" 目录下的对应于产品型号的json文件

配置文件格式：
-以下是配置文件的格式，遵循JSON格式的规范
-DLL接口读取的配置文件必须符合下述格式，否则判定结果为ERROR
-判定相关的限度信息和出料分类信息，从字段"检测项目列表"中获取
-"检测项目列表"中包含的检测项目数量可能多于下述示例，判定时需要根据实际输入的检测项名称进行匹配，使用对应的限度信息进行判定和出料分类
{
  "配置版本": "1.0",
  "更新时间": "2025-07-09 15:30:00",
  "说明": "质量检测配置参数",
  "型号名称": "0523A-F",
  "片数": "5",
  "检测项目列表": [
    {
      "检测项目": "外圆面伤",
      "分属相机": "相机1",
      "严重分类": "4",
      "轻微分类": "3",
      "良品分类": "0",
      "轻微上限": 99999,
      "良品上限": 200,
      "良品下限": 0,
      "轻微下限": 200,
      "偏移量": 0.0,
      "预处理模式": ""
    },
    {
      "检测项目": "外圆纹路",
      "分属相机": "相机1",
      "严重分类": "4",
      "轻微分类": "3",
      "良品分类": "0",
      "轻微上限": 99999,
      "良品上限": 800,
      "良品下限": 0,
      "轻微下限": 800,
      "偏移量": 0.0,
      "预处理模式": ""
    },
    {
      "检测项目": "槽内多铜",
      "分属相机": "相机1A",
      "严重分类": "4",
      "轻微分类": "2",
      "良品分类": "0",
      "轻微上限": 99999,
      "良品上限": 80,
      "良品下限": 0,
      "轻微下限": 80,
      "偏移量": 0.0,
      "预处理模式": ""
    },
    {
      "检测项目": "槽深",
      "分属相机": "相机5",
      "严重分类": "4",
      "轻微分类": "5",
      "良品分类": "1",
      "轻微上限": 99999.0,
      "良品上限": 0.8,
      "良品下限": 0.6,
      "轻微下限": 0.5,
      "偏移量": 0.0,
      "预处理模式": ""
    },
    {
      "检测项目": "槽宽",
      "分属相机": "相机5",
      "严重分类": "4",
      "轻微分类": "5",
      "良品分类": "1",
      "轻微上限": 99999.0,
      "良品上限": 0.45,
      "良品下限": 0.35,
      "轻微下限": 0.3,
      "偏移量": 0.0,
      "预处理模式": ""
    },
    {
      "检测项目": "槽槽夹角",
      "分属相机": "相机5",
      "严重分类": "4",
      "轻微分类": "5",
      "良品分类": "1",
      "轻微上限": 99999.0,
      "良品上限": 200.0,
      "良品下限": -200.0,
      "轻微下限": -300.0,
      "偏移量": 0.0,
      "预处理模式": ""
    }
  ]
}

