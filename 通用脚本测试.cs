using System;
using System.Text;
using System.IO;
using System.Collections.Generic;

/// <summary>
/// 通用Jud4Script脚本测试程序
/// 验证动态配置读取和参数转换功能
/// </summary>
public class UniversalScriptTest
{
    public static void Main(string[] args)
    {
        Console.WriteLine("===========================================");
        Console.WriteLine("    通用Jud4Script脚本测试程序");
        Console.WriteLine("===========================================");
        Console.WriteLine();

        var tester = new UniversalScriptTest();
        tester.RunAllTests();
        
        Console.WriteLine("===========================================");
        Console.WriteLine("测试完成！按任意键退出...");
        Console.ReadKey();
    }
    
    public void RunAllTests()
    {
        Console.WriteLine("1. 测试配置文件读取功能");
        TestConfigReading();
        Console.WriteLine();
        
        Console.WriteLine("2. 测试相机编号识别");
        TestCameraIdDetection();
        Console.WriteLine();
        
        Console.WriteLine("3. 测试通用转换功能");
        TestUniversalConversion();
        Console.WriteLine();
        
        Console.WriteLine("4. 测试容错机制");
        TestErrorHandling();
        Console.WriteLine();
    }
    
    private void TestConfigReading()
    {
        // 创建测试配置文件
        string testConfigPath = "TEST-001.json";
        string testConfig = @"{
  ""检测项目列表"": [
    {""检测项目"": ""外圆面伤"", ""分属相机"": ""相机1""},
    {""检测项目"": ""外圆纹路"", ""分属相机"": ""相机1""},
    {""检测项目"": ""表面缺陷"", ""分属相机"": ""相机1""}
  ]
}";
        
        try
        {
            File.WriteAllText(testConfigPath, testConfig, Encoding.UTF8);
            
            // 测试读取检测项目
            string[] defectNames = GetDefectItemNames("TEST-001");
            Console.WriteLine("  读取到的检测项目：");
            for (int i = 0; i < defectNames.Length; i++)
            {
                Console.WriteLine("    " + (i + 1) + ". " + defectNames[i]);
            }
            
            bool isValid = defectNames.Length == 3 && 
                          defectNames[0] == "外圆面伤" && 
                          defectNames[1] == "外圆纹路" && 
                          defectNames[2] == "表面缺陷";
            
            Console.WriteLine("  验证结果: " + (isValid ? "✅ 通过" : "❌ 失败"));
            
            // 清理测试文件
            if (File.Exists(testConfigPath))
                File.Delete(testConfigPath);
        }
        catch (Exception ex)
        {
            Console.WriteLine("  ❌ 测试异常: " + ex.Message);
        }
    }
    
    private void TestCameraIdDetection()
    {
        // 测试不同相机编号的识别
        string[] testConfigs = {
            @"{""检测项目列表"":[{""分属相机"":""相机1""}]}",
            @"{""检测项目列表"":[{""分属相机"":""相机5""}]}",
            @"{""检测项目列表"":[{""分属相机"":""相机2""}]}"
        };
        
        string[] expectedCameras = { "C1", "C5", "C2" };
        
        for (int i = 0; i < testConfigs.Length; i++)
        {
            string testFile = "CAM-TEST-" + i + ".json";
            try
            {
                File.WriteAllText(testFile, testConfigs[i], Encoding.UTF8);
                string cameraId = GetCameraId("CAM-TEST-" + i);
                
                Console.WriteLine("  测试配置 " + (i + 1) + ": " + testConfigs[i]);
                Console.WriteLine("  识别结果: " + cameraId);
                Console.WriteLine("  验证: " + (cameraId == expectedCameras[i] ? "✅" : "❌"));
                Console.WriteLine();
                
                if (File.Exists(testFile))
                    File.Delete(testFile);
            }
            catch (Exception ex)
            {
                Console.WriteLine("  ❌ 测试异常: " + ex.Message);
            }
        }
    }
    
    private void TestUniversalConversion()
    {
        // 创建测试配置
        string configContent = @"{
  ""检测项目列表"": [
    {""检测项目"": ""项目A"", ""分属相机"": ""相机1""},
    {""检测项目"": ""项目B"", ""分属相机"": ""相机1""},
    {""检测项目"": ""项目C"", ""分属相机"": ""相机1""}
  ]
}";
        
        string testFile = "UNIVERSAL-TEST.json";
        try
        {
            File.WriteAllText(testFile, configContent, Encoding.UTF8);
            
            // 测试不同输入格式
            string[] testInputs = {
                "100.0,200.0,300.0",
                "项目A=150.0;项目B=250.0;项目C=350.0",
                "100.0;200.0;300.0",
                "500.0"
            };
            
            string[] expectedOutputs = {
                "CAM=C1;TS=;JD=;;Items=;项目A=100.0;项目B=200.0;项目C=300.0;",
                "CAM=C1;TS=;JD=;;Items=;项目A=150.0;项目B=250.0;项目C=350.0;",
                "CAM=C1;TS=;JD=;;Items=;项目A=100.0;项目B=200.0;项目C=300.0;",
                "CAM=C1;TS=;JD=;;Items=;项目A=500.0;"
            };
            
            for (int i = 0; i < testInputs.Length; i++)
            {
                string result = ConvertIn0ToInspecInfo(testInputs[i], "UNIVERSAL-TEST");
                Console.WriteLine("  输入 " + (i + 1) + ": " + testInputs[i]);
                Console.WriteLine("  输出: " + result);
                Console.WriteLine("  验证: " + (result == expectedOutputs[i] ? "✅" : "❌"));
                Console.WriteLine();
            }
            
            if (File.Exists(testFile))
                File.Delete(testFile);
        }
        catch (Exception ex)
        {
            Console.WriteLine("  ❌ 测试异常: " + ex.Message);
        }
    }
    
    private void TestErrorHandling()
    {
        Console.WriteLine("  测试配置文件不存在的情况：");
        
        // 测试不存在的配置文件
        string[] defectNames = GetDefectItemNames("NOT-EXIST");
        Console.WriteLine("  默认检测项目: " + string.Join(", ", defectNames));
        
        string cameraId = GetCameraId("NOT-EXIST");
        Console.WriteLine("  默认相机编号: " + cameraId);
        
        string result = ConvertIn0ToInspecInfo("100.0,200.0", "NOT-EXIST");
        Console.WriteLine("  转换结果: " + result);
        
        bool isValid = defectNames.Length > 0 && cameraId == "C1" && result.Contains("CAM=C1");
        Console.WriteLine("  容错验证: " + (isValid ? "✅ 通过" : "❌ 失败"));
    }
    
    // 复制核心函数用于测试
    private string[] GetDefectItemNames(string modelNo)
    {
        try
        {
            string configPath = modelNo + ".json";
            
            if (!File.Exists(configPath))
            {
                return new string[] { "检测项目1", "检测项目2", "检测项目3" };
            }
            
            string jsonContent = File.ReadAllText(configPath, Encoding.UTF8);
            List<string> defectNames = new List<string>();
            
            string[] lines = jsonContent.Split('\n');
            foreach (string line in lines)
            {
                string trimmedLine = line.Trim();
                if (trimmedLine.StartsWith("\"检测项目\"") && trimmedLine.Contains(":"))
                {
                    int colonIndex = trimmedLine.IndexOf(":");
                    if (colonIndex > 0)
                    {
                        string valuepart = trimmedLine.Substring(colonIndex + 1).Trim();
                        valuepart = valuepart.TrimEnd(',').Trim();
                        if (valuepart.StartsWith("\"") && valuepart.EndsWith("\""))
                        {
                            string itemName = valuepart.Substring(1, valuepart.Length - 2);
                            if (!string.IsNullOrEmpty(itemName))
                            {
                                defectNames.Add(itemName);
                            }
                        }
                    }
                }
            }
            
            if (defectNames.Count > 0)
            {
                return defectNames.ToArray();
            }
            else
            {
                return new string[] { "检测项目1", "检测项目2", "检测项目3" };
            }
        }
        catch
        {
            return new string[] { "检测项目1", "检测项目2", "检测项目3" };
        }
    }
    
    private string GetCameraId(string modelNo)
    {
        try
        {
            string configPath = modelNo + ".json";
            
            if (File.Exists(configPath))
            {
                string jsonContent = File.ReadAllText(configPath, Encoding.UTF8);
                string[] lines = jsonContent.Split('\n');
                
                foreach (string line in lines)
                {
                    string trimmedLine = line.Trim();
                    if (trimmedLine.StartsWith("\"分属相机\"") && trimmedLine.Contains(":"))
                    {
                        int colonIndex = trimmedLine.IndexOf(":");
                        if (colonIndex > 0)
                        {
                            string valuepart = trimmedLine.Substring(colonIndex + 1).Trim();
                            valuepart = valuepart.TrimEnd(',').Trim();
                            if (valuepart.StartsWith("\"") && valuepart.EndsWith("\""))
                            {
                                string cameraName = valuepart.Substring(1, valuepart.Length - 2);
                                if (cameraName.Contains("1"))
                                    return "C1";
                                else if (cameraName.Contains("5"))
                                    return "C5";
                                else if (cameraName.Contains("2"))
                                    return "C2";
                                else if (cameraName.Contains("3"))
                                    return "C3";
                                else if (cameraName.Contains("4"))
                                    return "C4";
                            }
                        }
                        break;
                    }
                }
            }
        }
        catch
        {
        }
        
        return "C1";
    }
    
    private string ConvertIn0ToInspecInfo(string in0Value, string modelNo)
    {
        if (string.IsNullOrEmpty(in0Value))
        {
            return "CAM=C1;TS=;JD=;;Items=;";
        }
        
        StringBuilder inspecInfoBuilder = new StringBuilder();
        string cameraId = GetCameraId(modelNo);
        inspecInfoBuilder.Append("CAM=" + cameraId + ";TS=;JD=;;Items=;");
        
        if (in0Value.Contains("="))
        {
            // 键值对格式
            string[] pairs = in0Value.Split(';');
            foreach (string pair in pairs)
            {
                if (!string.IsNullOrWhiteSpace(pair) && pair.Contains("="))
                {
                    string cleanPair = pair.Trim();
                    string[] keyValue = cleanPair.Split('=');
                    if (keyValue.Length == 2)
                    {
                        string defectName = keyValue[0].Trim();
                        string defectValue = keyValue[1].Trim();
                        inspecInfoBuilder.Append(defectName + "=" + defectValue + ";");
                    }
                }
            }
        }
        else if (in0Value.Contains(","))
        {
            // 逗号分隔格式
            string[] values = in0Value.Split(',');
            string[] defectNames = GetDefectItemNames(modelNo);
            
            for (int i = 0; i < values.Length && i < defectNames.Length; i++)
            {
                string value = values[i].Trim();
                if (!string.IsNullOrWhiteSpace(value))
                {
                    inspecInfoBuilder.Append(defectNames[i] + "=" + value + ";");
                }
            }
        }
        else if (in0Value.Contains(";"))
        {
            // 分号分隔格式
            string[] values = in0Value.Split(';');
            string[] defectNames = GetDefectItemNames(modelNo);
            int itemIndex = 0;
            
            foreach (string value in values)
            {
                string cleanValue = value.Trim();
                if (!string.IsNullOrWhiteSpace(cleanValue) && itemIndex < defectNames.Length)
                {
                    inspecInfoBuilder.Append(defectNames[itemIndex] + "=" + cleanValue + ";");
                    itemIndex++;
                }
            }
        }
        else
        {
            // 单个数值
            string[] defectNames = GetDefectItemNames(modelNo);
            inspecInfoBuilder.Append(defectNames[0] + "=" + in0Value + ";");
        }
        
        return inspecInfoBuilder.ToString();
    }
}
