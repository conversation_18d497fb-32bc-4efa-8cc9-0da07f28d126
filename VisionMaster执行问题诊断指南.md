# VisionMaster 4.3.0 + Jud4Script 执行问题诊断指南

## 🔍 **常见问题分析**

### 1. **调试模式问题**
**问题**: JudgmentEngine.cs中 `_debugEnabled = true` 导致性能问题
**症状**: 
- 脚本执行缓慢
- 内存占用增加
- VisionMaster界面卡顿

**解决方案**:
```csharp
// 在Init()函数中关闭调试模式
JudgmentEngine.SetDebugEnabled(false);
```

### 2. **输入格式不匹配**
**问题**: VisionMaster输出格式与Jud4Script期望格式不一致
**VisionMaster输出**: `"外圆碰伤V1.250;外圆纹路V0.500"`
**Jud4Script期望**: `"Items=;槽深=0.7;槽宽=0.4"`

**解决方案**: 使用转换函数统一格式

### 3. **配置文件问题**
**问题**: 配置文件路径或格式错误
**检查项**:
- [ ] 配置文件是否存在：`D:\LvConfig\产品型号\{modelNo}.json`
- [ ] 文件编码是否为UTF-8
- [ ] JSON格式是否正确
- [ ] 检测项目名称是否完全匹配

### 4. **DLL依赖问题**
**问题**: Jud4Script.dll或依赖库缺失
**检查项**:
- [ ] Jud4Script.dll是否在VisionMaster程序目录
- [ ] Newtonsoft.Json.dll是否存在
- [ ] .NET Framework版本是否兼容

## 🛠️ **解决步骤**

### 步骤1: 检查DLL文件
```
VisionMaster安装目录/
├── Jud4Script.dll          ✓ 必需
├── Newtonsoft.Json.dll     ✓ 必需
└── 其他依赖文件...
```

### 步骤2: 验证配置文件
```json
{
  "配置版本": "1.0",
  "型号名称": "0523A-F",
  "检测项目列表": [
    {
      "检测项目": "槽深",
      "分属相机": "相机5",
      "严重分类": "9",
      "轻微分类": "5",
      "良品分类": "1",
      "轻微上限": 0.85,
      "良品上限": 0.8,
      "良品下限": 0.6,
      "轻微下限": 0.5
    }
  ]
}
```

### 步骤3: 使用修正版脚本
使用提供的 `VisionMaster-Jud4Script集成脚本.cs`，它包含：
- 调试模式关闭
- 格式转换优化
- 异常处理增强
- VisionMaster兼容性改进

### 步骤4: 测试验证
```csharp
// 测试输入
modelNo = "0523A-F";
in0 = "槽深V0.75;槽宽V0.45";

// 期望输出
out0 = "1"; // 或 "5", "9", "0"
```

## 🚨 **常见错误代码**

| 返回值 | 含义 | 可能原因 |
|--------|------|----------|
| "0" | 异常分类 | 配置文件错误、DLL缺失、格式错误 |
| "1" | 良品分类 | 正常，所有检测项都在良品范围内 |
| "5" | 轻微分类 | 有检测项在轻微范围内 |
| "9" | 严重分类 | 有检测项超出轻微上限 |

## 🔧 **调试方法**

### 方法1: 启用调试信息
```csharp
// 临时启用调试（仅用于问题排查）
JudgmentEngine.SetDebugEnabled(true);
string debugInfo = JudgmentEngine.GetDebugInfo();
// 查看debugInfo内容
JudgmentEngine.SetDebugEnabled(false); // 排查完毕后关闭
```

### 方法2: 分步验证
```csharp
// 1. 验证配置文件加载
bool configExists = System.IO.File.Exists(@"D:\LvConfig\产品型号\" + modelNo + ".json");

// 2. 验证输入格式
string convertedInput = ConvertToJud4ScriptFormat(in0);

// 3. 验证判定调用
string result = JudgmentEngine.Evaluate(modelNo, convertedInput);
```

### 方法3: 日志记录
```csharp
// 记录关键信息到文件
private void LogToFile(string message)
{
    try
    {
        string logPath = @"D:\VisionMaster_Jud4Script_Log.txt";
        string logEntry = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + " - " + message + "\r\n";
        System.IO.File.AppendAllText(logPath, logEntry);
    }
    catch
    {
        // 忽略日志记录异常
    }
}
```

## ⚡ **性能优化建议**

### 1. 关闭调试模式
```csharp
JudgmentEngine.SetDebugEnabled(false);
```

### 2. 缓存配置信息
```csharp
// 避免每次都重新加载配置文件
private static Dictionary<string, object> configCache = new Dictionary<string, object>();
```

### 3. 异常处理优化
```csharp
// 使用try-catch包装关键调用，避免脚本中断
try
{
    string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);
    out0 = result;
}
catch
{
    out0 = "0"; // 异常时返回默认值
}
```

## 📋 **检查清单**

在VisionMaster中部署前，请确认：

- [ ] Jud4Script.dll已复制到VisionMaster程序目录
- [ ] 配置文件已放置在正确路径
- [ ] 配置文件格式正确，编码为UTF-8
- [ ] 检测项目名称与VisionMaster输出完全匹配
- [ ] 脚本中已关闭调试模式
- [ ] 异常处理已添加
- [ ] 输入输出变量名称正确设置

## 🎯 **快速验证**

使用以下代码快速验证集成是否正常：

```csharp
public bool Process()
{
    try
    {
        // 简单测试
        string testInput = "Items=;槽深=0.75";
        string result = JudgmentEngine.Evaluate("0523A-F", testInput);
        out0 = result;
        return true;
    }
    catch (Exception ex)
    {
        out0 = "ERROR: " + ex.Message;
        return false;
    }
}
```

如果返回 "ERROR: ..." 则说明有集成问题需要解决。
如果返回 "1", "5", "9" 等数字则说明集成正常。
