# Jud4Script 快速使用指南

## 📋 概述

本指南帮助您快速在VisionMaster中集成Jud4Script，实现将in0参数转换为inspecInfo格式进行产品质量判定。

## 🚀 快速开始

### 1. 验证代码编译

```bash
# 运行编译验证脚本
双击：编译验证.bat
```

这将验证代码是否可以正常编译，并可选择运行测试程序。

### 2. 集成到VisionMaster

将 `配置-Jud4Script.cs` 中的代码复制到您的VisionMaster项目中。

### 3. 设置输入输出变量

**输入变量**：
- `modelNo` (string): 产品型号，如 "0523A-F"
- `in0` (string): 缺陷检测数据

**输出变量**：
- `out0` (string): 判定结果 ("1"=良品, "3"=轻微, "4"=严重, "0"=异常)

## 🎯 核心功能

### 参数转换逻辑

**C1代表相机1(CAM1)**，系统会自动将各种格式的in0参数转换为标准格式：

#### 格式1：单个缺陷值
```csharp
in0 = "1.250"
→ inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆碰伤=1.250;"
```

#### 格式2：键值对格式
```csharp
in0 = "外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000"
→ inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"
```

#### 格式3：逗号分隔格式
```csharp
in0 = "1.250,0.500,0.000"
→ inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"
```

#### 格式4：分号分隔格式
```csharp
in0 = "1.250;0.500;0.000"
→ inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"
```

## 🔧 缺陷项目映射

系统预定义了8个缺陷项目，按顺序对应：

| 序号 | 缺陷项目 | 说明 |
|------|---------|------|
| 1 | 外圆碰伤 | 外圆表面碰撞损伤 |
| 2 | 外圆纹路 | 外圆表面纹理缺陷 |
| 3 | 槽内多铜 | 槽内多余铜材料 |
| 4 | 槽深异常 | 槽深度不符合要求 |
| 5 | 槽宽异常 | 槽宽度不符合要求 |
| 6 | 表面划伤 | 表面划痕缺陷 |
| 7 | 尺寸偏差 | 整体尺寸偏差 |
| 8 | 形状变形 | 产品形状变形 |

**注意**：可以根据实际需要在代码中的`GetDefectItemNames()`函数中修改这些缺陷项目名称。

## 📊 配置文件设置

在 `D:\LvConfig\产品型号\{modelNo}.json` 创建配置文件：

```json
{
  "配置版本": "1.0",
  "型号名称": "0523A-F",
  "检测项目列表": [
    {
      "检测项目": "外圆碰伤",
      "分属相机": "相机1",
      "严重分类": "4",
      "轻微分类": "3",
      "良品分类": "1",
      "轻微上限": 2.000,
      "良品上限": 1.000,
      "良品下限": 0.000,
      "轻微下限": 0.000
    },
    {
      "检测项目": "外圆纹路",
      "分属相机": "相机1",
      "严重分类": "4",
      "轻微分类": "3",
      "良品分类": "1",
      "轻微上限": 1.000,
      "良品上限": 0.600,
      "良品下限": 0.000,
      "轻微下限": 0.000
    }
  ]
}
```

## 🔍 使用示例

### VisionMaster脚本调用

```csharp
public bool Process()
{
    try
    {
        // 设置输入参数
        modelNo = "0523A-F";
        in0 = "外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000";
        
        // 系统会自动转换并调用Jud4Script
        // 结果会输出到out0变量
        
        return true;
    }
    catch (Exception ex)
    {
        out0 = "0"; // 异常时返回0
        return false;
    }
}
```

### 判定结果说明

- **"1"** - 良品：所有缺陷值都在良品范围内
- **"3"** - 轻微：有缺陷值在轻微范围内，但无严重缺陷
- **"4"** - 严重：有缺陷值超过轻微上限
- **"0"** - 异常：配置错误或系统异常

## 📝 日志记录

系统会自动在 `D:\HODD\Jud4Script_Log.txt` 记录详细信息：

```
2025-07-12 10:30:00.123 - 产品型号: 0523A-F, 检测信息: CAM=C1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;, 判定结果: 3
```

## ⚠️ 注意事项

1. **配置文件路径**：必须放在 `D:\LvConfig\产品型号\{modelNo}.json`
2. **缺陷名称匹配**：确保in0中的缺陷名称与配置文件完全一致
3. **数值格式**：建议使用3位小数格式（如1.250）
4. **编码格式**：配置文件必须使用UTF-8编码
5. **依赖文件**：确保Jud4Script.dll和Newtonsoft.Json.dll在同一目录

## 🛠️ 故障排除

### 常见问题

1. **返回结果为"0"**
   - 检查配置文件是否存在且格式正确
   - 确认缺陷项目名称是否匹配
   - 查看日志文件了解具体错误

2. **编译错误**
   - 运行 `编译验证.bat` 检查代码语法
   - 确保.NET Framework版本兼容

3. **判定结果不正确**
   - 检查配置文件中的阈值设置
   - 验证输入数据格式是否正确

### 调试建议

1. **启用详细日志**：查看 `D:\HODD\Jud4Script_Log.txt`
2. **运行测试程序**：使用 `Jud4Script转换测试.cs` 验证转换功能
3. **检查配置文件**：确认JSON格式和路径正确

## 📞 技术支持

如需进一步帮助，请提供：
1. VisionMaster版本信息
2. 具体的错误信息或日志
3. 输入数据示例
4. 配置文件内容

---

**重要提醒**：
- 在生产环境使用前，请充分测试各种输入格式
- 定期备份配置文件
- 监控日志文件以及时发现问题
