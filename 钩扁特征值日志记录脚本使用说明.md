# 钩扁特征值日志记录脚本使用说明

## 概述

本脚本用于处理钩扁检测数据，根据输入的角度(dangle)和钩外径(dis)计算特征值统计信息，并将结果连同照片名称一起记录到每日日志文件中。

## 版本信息

- **文件名**: `C5钩扁特征值查找-new.cs`
- **版本**: 1.0
- **更新日期**: 2025-07-10
- **兼容性**: VisionMaster 4.3.0, .NET Framework 4.6.1

## 主要功能

### 1. 特征值计算
- 解析分号分隔的角度和距离数据
- 计算最小值、最大值、平均值等统计信息
- 生成特征值描述字符串

### 2. 每日日志记录
- 自动按日期创建日志文件
- 记录时间戳、照片名称和特征值信息
- 存储位置：`D:\HODD\`

### 3. 自动目录管理
- 首次运行自动创建HODD目录
- 无需手动创建存储路径

## 输入参数

### dangle (string)
- **描述**: 角度参数字符串
- **格式**: 分号分隔的数值
- **示例**: `"107.780;137.780;167.779;197.779;227.779;257.780;287.780;317.781;347.781;17.783;47.779;77.782"`

### dis (string)
- **描述**: 钩外径参数字符串
- **格式**: 分号分隔的数值
- **示例**: `"385.184;384.995;384.800;384.726;384.751;384.975;385.158;385.488;385.502;386.174;384.865;386.023"`

### picNam (string)
- **描述**: 照片名称
- **格式**: 字符串
- **示例**: `"138_20250604204122298.bmp"`

## 输出结果

### 日志文件格式
- **文件位置**: `D:\HODD\HookFeature_YYYY-MM-DD.log`
- **文件命名**: 按日期自动命名，每天一个文件
- **内容格式**: `[HH:mm:ss.fff] 照片:照片名称 | 特征值:统计信息`

### 日志内容示例
```
[14:08:35.123] 照片:138_20250604204122298.bmp | 特征值:角度统计[数量:12, 最小:17.783, 最大:347.781, 平均:197.779] 距离统计[数量:12, 最小:384.726, 最大:386.174, 平均:385.203]
[14:09:12.456] 照片:139_20250604204125456.bmp | 特征值:角度统计[数量:12, 最小:18.234, 最大:348.123, 平均:198.456] 距离统计[数量:12, 最小:384.567, 最大:386.234, 平均:385.123]
```

## 特征值统计信息

脚本会计算以下统计信息：

### 角度统计
- **数量**: 有效角度值的个数
- **最小值**: 角度的最小值
- **最大值**: 角度的最大值
- **平均值**: 角度的平均值

### 距离统计
- **数量**: 有效距离值的个数
- **最小值**: 距离的最小值
- **最大值**: 距离的最大值
- **平均值**: 距离的平均值

## 文件结构

```
D:\HODD/                          # 日志存储根目录（自动创建）
├── HookFeature_2025-07-10.log   # 当日日志文件
├── HookFeature_2025-07-11.log   # 次日日志文件
└── ...

项目目录/
└── C5钩扁特征值查找-new.cs      # 主脚本文件
```

## 使用步骤

### 1. 部署脚本
1. 将 `C5钩扁特征值查找-new.cs` 复制到VisionMaster项目目录
2. 脚本会自动创建 `D:\HODD` 目录

### 2. 配置VisionMaster流程
1. 在流程中添加脚本模块
2. 设置输入变量：
   - `dangle`: 连接角度数据输出
   - `dis`: 连接钩外径数据输出
   - `picNam`: 连接照片名称

### 3. 运行和监控
1. 启动VisionMaster流程
2. 脚本会自动处理输入数据并记录到日志
3. 查看 `D:\HODD\HookFeature_YYYY-MM-DD.log` 了解处理结果

## 错误处理

### 常见问题

1. **输入参数不完整**
   - **现象**: 控制台显示"输入参数不完整"
   - **原因**: dangle、dis或picNam参数为空
   - **解决方案**: 检查输入变量连接是否正确

2. **特征值解析失败**
   - **现象**: 控制台显示"特征值解析失败，无有效数据"
   - **原因**: 输入字符串格式错误或无有效数值
   - **解决方案**: 检查输入数据格式，确保使用分号分隔

3. **日志写入失败**
   - **现象**: 控制台显示"写入日志失败"
   - **原因**: 权限不足或磁盘空间不够
   - **解决方案**: 
     - 以管理员身份运行VisionMaster
     - 检查D盘剩余空间
     - 确认D:\HODD目录权限

## 监控和维护

### 日志文件管理
- **定期清理**: 建议定期备份和清理旧的日志文件
- **空间监控**: 监控D盘剩余空间
- **权限检查**: 确保对D:\HODD目录有写入权限

### 性能优化
- **数据量控制**: 如果单日数据量很大，考虑按小时分割日志
- **格式优化**: 根据需要调整日志格式和精度

## 数据分析

日志文件可用于：
- **质量统计**: 分析角度和距离的分布情况
- **趋势分析**: 观察特征值随时间的变化
- **异常检测**: 识别超出正常范围的数值
- **生产监控**: 实时了解生产状态

## 扩展功能

可以考虑的扩展：
- 添加数据验证和异常值检测
- 实现数据导出功能（CSV、Excel等）
- 添加统计报表生成
- 集成数据库存储

## 技术支持

如遇到问题，请提供以下信息：
1. VisionMaster版本信息
2. 输入数据示例
3. 错误信息截图
4. 日志文件内容

---

**注意**: 本脚本专门用于钩扁特征值的日志记录，适合生产环境的数据收集和分析需求。
