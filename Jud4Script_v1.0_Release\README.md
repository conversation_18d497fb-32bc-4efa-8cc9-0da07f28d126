# Jud4Script.dll 安装包

## 概述
Jud4Script.dll 是为VisionMaster开发的动态判定库，实现了基于配置文件的检测项目判定功能。

## 版本信息
- **版本**: 1.0
- **发布日期**: 2025年7月
- **兼容性**: VisionMaster 4.3.0+, .NET Framework 4.8+
- **依赖**: Newtonsoft.Json ********

## 安装包内容

```
Jud4Script_v1.0_Release/
├── README.md                    # 本文件
├── install.bat                  # Windows批处理安装脚本
├── install.ps1                  # PowerShell安装脚本
├── bin/                         # 核心DLL文件
│   ├── Jud4Script.dll          # 主要动态库
│   └── Newtonsoft.Json.dll     # JSON处理依赖库
├── config_samples/              # 配置文件示例
│   ├── Sample00.json           # 标准配置示例
│   ├── 0523A-F.json            # 槽类检测示例
│   ├── C5.json                 # 综合检测示例
│   └── 配置文件说明.txt         # 配置文件说明
├── docs/                        # 详细文档
│   ├── 部署和使用说明.md        # 详细部署指南
│   ├── 配置文件示例和说明.md    # 配置文件详细说明
│   ├── 问题解决记录.md          # 故障排除指南
│   └── README.md               # 项目说明
└── test/                        # 测试程序
    ├── TestJud4Script.exe      # 安装验证测试程序
    └── TestJud4Script.cs       # 测试程序源码
```

## 快速安装

### 方法1：自动安装（推荐）
1. **以管理员身份运行** `install.bat` 或 `install.ps1`
2. 脚本会自动：
   - 创建配置目录 `D:\LvConfig\产品型号\`
   - 复制配置文件示例
   - 运行安装验证测试
3. 将 `bin\` 目录中的DLL文件复制到您的VisionMaster项目目录

### 方法2：手动安装
1. **创建配置目录**：`D:\LvConfig\产品型号\`
2. **复制DLL文件**：将 `bin\Jud4Script.dll` 和 `bin\Newtonsoft.Json.dll` 复制到您的项目目录
3. **配置产品参数**：参考 `config_samples\` 中的示例创建您的配置文件
4. **运行测试**：执行 `test\TestJud4Script.exe` 验证安装

## 使用方法

### 在VisionMaster脚本中使用
```csharp
using Jud4Script;

// 调用判定方法
string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);
```

### 参数说明
- **modelNo**: 产品型号（字符串）
- **inspecInfo**: 检测信息字符串，格式：`"CAM=C5;TS=;JD=;;Items=;外圆面伤=180.0;外圆纹路=0.0;槽内多铜=0.0;"`
- **返回值**: 出料分类（字符串）
  - `"1"` - 良品分类
  - `"2"/"3"/"4"` - 轻微/严重分类（根据配置）
  - `"0"` - 异常分类（错误）

## 配置文件要求

### 文件位置
配置文件必须放在：`D:\LvConfig\产品型号\{modelNo}.json`

### 文件格式
```json
{
  "配置版本": "1.0",
  "更新时间": "2025-07-11 00:30:00",
  "说明": "质量检测配置参数",
  "型号名称": "产品型号",
  "片数": "3",
  "检测项目列表": [
    {
      "检测项目": "外圆面伤",
      "分属相机": "相机1",
      "严重分类": "4",
      "轻微分类": "3",
      "良品分类": "0",
      "轻微上限": 99999.0,
      "良品上限": 200.0,
      "良品下限": 0.0,
      "轻微下限": 200.0,
      "偏移量": 0.0,
      "预处理模式": ""
    }
  ]
}
```

### 重要约束
- 良品上限 ≥ 良品下限
- 轻微上限 ≥ 良品上限
- 轻微下限 ≤ 良品下限
- 分类代码必须在'1'-'9'范围内且不重复
- 文件必须使用UTF-8编码保存

## 验证安装

运行 `test\TestJud4Script.exe` 进行安装验证：
- ✅ DLL文件检查
- ✅ 配置目录检查
- ✅ 功能测试
- ✅ 异常处理测试

## 故障排除

### 常见问题
1. **依赖库缺失**: 确保Newtonsoft.Json.dll与Jud4Script.dll在同一目录
2. **配置文件编码**: 确保JSON文件使用UTF-8编码保存
3. **配置验证失败**: 检查数值约束是否满足要求
4. **输入格式错误**: 确保检测项名称与配置文件完全匹配

### 获取帮助
- 查看 `docs\` 目录中的详细文档
- 运行测试程序进行诊断
- 检查配置文件格式和约束

## 技术支持

如需技术支持，请提供：
1. 错误信息和返回值
2. 配置文件内容
3. 输入参数示例
4. VisionMaster版本信息

---

**重要提醒**: 
- 安装脚本需要管理员权限运行
- 配置文件必须使用UTF-8编码
- DLL文件必须与Newtonsoft.Json.dll在同一目录
