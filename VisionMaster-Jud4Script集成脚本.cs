using System;
using System.Text;
using System.Windows.Forms;
using Script.Methods;
using Jud4Script;

/************************************
VisionMaster 4.3.0 + Jud4Script v2.0.0 集成脚本
修正版本 - 解决执行问题
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
    //执行次数计数
    int processCount;

    /// <summary>
    /// Initialize the field's value when compiling
    /// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
        //变量初始化，其余变量可在该函数中添加
        processCount = 0;
        
        // 关闭调试模式以提高性能
        try
        {
            JudgmentEngine.SetDebugEnabled(false);
        }
        catch
        {
            // 忽略设置调试模式的异常
        }
    }

    /// <summary>
    /// Enter the process function when running code once
    /// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
        try
        {
            // 转换in0为Jud4Script v2.0.0需要的格式
            string inspecInfo = ConvertToJud4ScriptFormat(in0);
            
            // 调用Jud4Script判定引擎
            string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);
            
            // 输出结果
            out0 = result;
            
            return true;
        }
        catch (Exception ex)
        {
            // 异常处理
            out0 = "0"; // 异常时返回0
            
            // 可选：记录异常信息到VisionMaster日志
            try
            {
                // 如果VisionMaster支持日志记录
                // LogMessage("Jud4Script异常: " + ex.Message);
            }
            catch
            {
                // 忽略日志记录异常
            }
            
            return false;
        }
    }

    /// <summary>
    /// 将VisionMaster的in0参数转换为Jud4Script v2.0.0需要的格式
    /// </summary>
    /// <param name="in0Value">VisionMaster输入参数</param>
    /// <returns>Jud4Script格式的字符串</returns>
    private string ConvertToJud4ScriptFormat(string in0Value)
    {
        if (string.IsNullOrEmpty(in0Value))
        {
            return "Items=;";
        }

        StringBuilder inspecInfoBuilder = new StringBuilder();
        
        // Jud4Script v2.0.0期望的基本格式
        // 不包含CAM、TS等前缀，直接从Items开始
        inspecInfoBuilder.Append("Items=;");

        // 智能分割和转换
        string[] items;
        if (in0Value.Contains(";"))
        {
            items = in0Value.Split(';');
        }
        else if (in0Value.Contains(","))
        {
            items = in0Value.Split(',');
        }
        else
        {
            items = new string[] { in0Value };
        }

        // 处理每个检测项
        foreach (string item in items)
        {
            string trimmedItem = item.Trim();
            if (!string.IsNullOrEmpty(trimmedItem))
            {
                // 处理格式转换
                string processedItem = ProcessDetectionItem(trimmedItem);
                if (!string.IsNullOrEmpty(processedItem))
                {
                    inspecInfoBuilder.Append(processedItem);
                    if (!processedItem.EndsWith(";"))
                    {
                        inspecInfoBuilder.Append(";");
                    }
                }
            }
        }

        return inspecInfoBuilder.ToString();
    }

    /// <summary>
    /// 处理单个检测项，转换为Jud4Script格式
    /// </summary>
    /// <param name="item">单个检测项字符串</param>
    /// <returns>处理后的检测项字符串</returns>
    private string ProcessDetectionItem(string item)
    {
        if (string.IsNullOrEmpty(item))
            return "";

        // 移除可能的相机编号前缀（C1, C2, C5等）
        string processed = item;
        processed = processed.Replace("C1", "");
        processed = processed.Replace("C2A", "");
        processed = processed.Replace("C2", "");
        processed = processed.Replace("C3", "");
        processed = processed.Replace("C4", "");
        processed = processed.Replace("C5", "");

        // 将V替换为=
        processed = processed.Replace("V", "=");

        // 确保格式正确：检测项名称=数值
        if (!processed.Contains("="))
        {
            // 如果没有等号，可能是纯数值，需要添加默认检测项名称
            // 这种情况需要根据实际业务逻辑调整
            return ""; // 跳过无效格式
        }

        return processed;
    }

    /// <summary>
    /// 获取调试信息（仅在需要时使用）
    /// </summary>
    /// <returns>调试信息字符串</returns>
    private string GetDebugInfo()
    {
        try
        {
            return JudgmentEngine.GetDebugInfo();
        }
        catch
        {
            return "无法获取调试信息";
        }
    }

    /// <summary>
    /// 清空调试信息（仅在需要时使用）
    /// </summary>
    private void ClearDebugInfo()
    {
        try
        {
            JudgmentEngine.ClearDebugInfo();
        }
        catch
        {
            // 忽略清空调试信息的异常
        }
    }
}
