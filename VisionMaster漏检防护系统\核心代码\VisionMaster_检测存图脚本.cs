// VisionMaster 4.3.0 产品检测与存图脚本
// 功能：检测钩角dis参数，自动存图，漏检分析

using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using System.Globalization;

public class ProductDetectionScript
{
    // 配置参数
    private const double DIS_THRESHOLD = 385.5; // dis阈值，根据实际情况调整
    private const string SAVE_PATH = @"D:\HODD\ProductImages"; // 存图路径
    private const string LOG_PATH = @"D:\HODD\DetectionLog.txt"; // 日志路径
    private const double SIMILARITY_THRESHOLD = 2.0; // 相似度阈值
    
    // 全局变量
    private static List<ProductRecord> productHistory = new List<ProductRecord>();
    
    public class ProductRecord
    {
        public DateTime DetectionTime { get; set; }
        public string ImagePath { get; set; }
        public double[] DisValues { get; set; }
        public double[] DangleValues { get; set; }
        public bool IsNG { get; set; }
        public double MaxDis { get; set; }
        public string ProductId { get; set; }
    }
    
    // 主检测函数
    public string DetectProduct(string dangleStr, string disStr)
    {
        try
        {
            // 解析输入参数
            double[] dangleValues = ParseStringArray(dangleStr);
            double[] disValues = ParseStringArray(disStr);
            
            // 生成产品ID（基于时间戳）
            string productId = DateTime.Now.ToString("yyyyMMdd_HHmmss_fff");
            
            // 执行检测
            bool isNG = false;
            double maxDis = disValues.Max();
            
            // 检查dis阈值
            foreach (double dis in disValues)
            {
                if (dis > DIS_THRESHOLD)
                {
                    isNG = true;
                    break;
                }
            }
            
            // 保存图像
            string imagePath = SaveCurrentImage(productId, isNG);
            
            // 记录检测结果
            var record = new ProductRecord
            {
                DetectionTime = DateTime.Now,
                ImagePath = imagePath,
                DisValues = disValues,
                DangleValues = dangleValues,
                IsNG = isNG,
                MaxDis = maxDis,
                ProductId = productId
            };
            
            productHistory.Add(record);
            
            // 如果是OK产品，进行相似性检查
            if (!isNG)
            {
                CheckForSimilarNGProducts(record);
            }
            
            // 记录日志
            LogDetectionResult(record);
            
            // 输出结果
            string result = isNG ? "NG" : "OK";
            out0 = result; // VisionMaster外部输出变量
            
            return result;
        }
        catch (Exception ex)
        {
            LogError($"检测异常: {ex.Message}");
            out0 = "ERROR";
            return "ERROR";
        }
    }
    
    // 解析字符串数组
    private double[] ParseStringArray(string input)
    {
        if (string.IsNullOrEmpty(input))
            return new double[0];
            
        return input.Split(';', ',')
                   .Where(s => !string.IsNullOrWhiteSpace(s))
                   .Select(s => double.Parse(s.Trim(), CultureInfo.InvariantCulture))
                   .ToArray();
    }
    
    // 保存当前图像
    private string SaveCurrentImage(string productId, bool isNG)
    {
        try
        {
            // 确保目录存在
            string dateFolder = DateTime.Now.ToString("yyyyMMdd");
            string saveDir = Path.Combine(SAVE_PATH, dateFolder);
            
            if (!Directory.Exists(saveDir))
            {
                Directory.CreateDirectory(saveDir);
            }
            
            // 生成文件名
            string status = isNG ? "NG" : "OK";
            string fileName = $"{productId}_{status}.bmp";
            string fullPath = Path.Combine(saveDir, fileName);
            
            // 保存图像（VisionMaster API调用）
            // 注意：这里需要根据实际的VisionMaster API进行调整
            SaveImage(fullPath); // VisionMaster保存图像函数
            
            return fullPath;
        }
        catch (Exception ex)
        {
            LogError($"保存图像失败: {ex.Message}");
            return "";
        }
    }
    
    // 检查相似的NG产品
    private void CheckForSimilarNGProducts(ProductRecord currentRecord)
    {
        try
        {
            // 查找最近的NG产品
            var recentNGProducts = productHistory
                .Where(p => p.IsNG && 
                           (DateTime.Now - p.DetectionTime).TotalMinutes < 60) // 1小时内
                .ToList();
            
            foreach (var ngProduct in recentNGProducts)
            {
                double similarity = CalculateDisSimilarity(currentRecord.DisValues, ngProduct.DisValues);
                
                if (similarity < SIMILARITY_THRESHOLD)
                {
                    // 发现可能的漏检
                    string alertMsg = $"发现疑似漏检产品！\n" +
                                    $"当前OK产品: {currentRecord.ProductId}\n" +
                                    $"相似NG产品: {ngProduct.ProductId}\n" +
                                    $"相似度: {similarity:F3}\n" +
                                    $"当前最大dis: {currentRecord.MaxDis:F3}\n" +
                                    $"NG产品最大dis: {ngProduct.MaxDis:F3}";
                    
                    LogAlert(alertMsg);
                    
                    // 可选：触发报警或停机
                    // TriggerAlert(alertMsg);
                }
            }
        }
        catch (Exception ex)
        {
            LogError($"相似性检查异常: {ex.Message}");
        }
    }
    
    // 计算dis参数相似度
    private double CalculateDisSimilarity(double[] dis1, double[] dis2)
    {
        if (dis1.Length != dis2.Length)
            return double.MaxValue;
        
        double sumSquaredDiff = 0;
        for (int i = 0; i < dis1.Length; i++)
        {
            double diff = dis1[i] - dis2[i];
            sumSquaredDiff += diff * diff;
        }
        
        return Math.Sqrt(sumSquaredDiff / dis1.Length);
    }
    
    // 记录检测日志
    private void LogDetectionResult(ProductRecord record)
    {
        try
        {
            string logEntry = $"{record.DetectionTime:yyyy-MM-dd HH:mm:ss.fff}," +
                            $"{record.ProductId}," +
                            $"{(record.IsNG ? "NG" : "OK")}," +
                            $"{record.MaxDis:F3}," +
                            $"{string.Join(";", record.DisValues.Select(d => d.ToString("F3")))}," +
                            $"{record.ImagePath}";
            
            File.AppendAllText(LOG_PATH, logEntry + Environment.NewLine);
        }
        catch (Exception ex)
        {
            // 日志记录失败不影响主流程
            Console.WriteLine($"日志记录失败: {ex.Message}");
        }
    }
    
    // 记录警报日志
    private void LogAlert(string message)
    {
        try
        {
            string alertLog = $"[ALERT] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
            string alertPath = Path.Combine(Path.GetDirectoryName(LOG_PATH), "AlertLog.txt");
            File.AppendAllText(alertPath, alertLog + Environment.NewLine);
        }
        catch (Exception ex)
        {
            LogError($"警报日志记录失败: {ex.Message}");
        }
    }
    
    // 记录错误日志
    private void LogError(string message)
    {
        try
        {
            string errorLog = $"[ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
            string errorPath = Path.Combine(Path.GetDirectoryName(LOG_PATH), "ErrorLog.txt");
            File.AppendAllText(errorPath, errorLog + Environment.NewLine);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"错误日志记录失败: {ex.Message}");
        }
    }
    
    // VisionMaster图像保存函数（需要根据实际API调整）
    private void SaveImage(string filePath)
    {
        // 这里需要调用VisionMaster的实际图像保存API
        // 示例：
        // VisionMaster.SaveCurrentImage(filePath);
        // 或者
        // ImageProcessor.SaveImage(filePath);
    }
    
    // 清理历史记录（可选，防止内存占用过大）
    public void CleanupHistory()
    {
        var cutoffTime = DateTime.Now.AddHours(-24); // 保留24小时内的记录
        productHistory.RemoveAll(p => p.DetectionTime < cutoffTime);
    }
}

// 使用示例
public class ScriptEntry
{
    private static ProductDetectionScript detector = new ProductDetectionScript();
    
    public static void Main()
    {
        // VisionMaster脚本入口点
        // 假设从VisionMaster获取到的参数
        string dangleInput = "98.893;128.893;158.892;188.893;218.893;248.893;278.894;308.892;338.894;8.895;38.894;68.894";
        string disInput = "385.195;385.079;384.910;385.118;385.148;385.005;385.397;384.984;385.740;386.011;385.533;385.492";
        
        // 执行检测
        string result = detector.DetectProduct(dangleInput, disInput);
        
        // 定期清理历史记录
        if (DateTime.Now.Minute == 0) // 每小时清理一次
        {
            detector.CleanupHistory();
        }
    }
}
