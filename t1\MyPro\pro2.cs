// pro2.cs
using System;
using System.Linq;

// 主程序
var number = 42;
var message = "The answer to life, the universe, and everything is";
Console.WriteLine($"{message}, {number}."); 

// 方法定义
int Add(int a, int b) => a + b;
Console.WriteLine($"Sum of 1 and 2 is {Add(1, 2)}.");

// 偶数计算
var numbers = new[] { 1, 2, 3, 4, 5, 6, 7, 8, 9 };
Console.WriteLine("Even numbers: " + string.Join(", ", numbers.Where(n => n % 2 == 0)));

// 调用Pro2
Pro2.Count1();

// 异常处理
try
{
    int zero = 0;
    int _ = number / zero;
}
catch (DivideByZeroException ex)
{
    Console.WriteLine($"Math Error: {ex.Message}");
}
finally
{
    Console.WriteLine("清理资源...");
}