<?xml version="1.0" encoding="utf-8"?>
<HookFeatureConfiguration>
  <!-- 特征值匹配配置 -->
  <MatchingSettings>
    <!-- 角度阈值（度） -->
    <AngleThreshold>5.0</AngleThreshold>
    <!-- 距离阈值（像素） -->
    <DistanceThreshold>10.0</DistanceThreshold>
    <!-- 相似度阈值（0-1） -->
    <SimilarityThreshold>0.8</SimilarityThreshold>
    <!-- 最大匹配结果显示数量 -->
    <MaxDisplayMatches>5</MaxDisplayMatches>
  </MatchingSettings>
  
  <!-- 图像存储配置 -->
  <ImageStorage>
    <!-- 图像存储根目录 -->
    <RootDirectory>D:\HookImages</RootDirectory>
    <!-- 图像格式 -->
    <ImageFormat>bmp</ImageFormat>
    <!-- 是否按日期创建子文件夹 -->
    <CreateDateFolders>true</CreateDateFolders>
    <!-- 文件名前缀 -->
    <FileNamePrefix>Hook_</FileNamePrefix>
  </ImageStorage>
  
  <!-- 特征值数据库配置 -->
  <Database>
    <!-- 是否启用持久化存储 -->
    <EnablePersistence>true</EnablePersistence>
    <!-- 数据库文件路径 -->
    <DatabaseFile>D:\HookImages\FeatureDatabase.xml</DatabaseFile>
    <!-- 最大存储特征值数量 -->
    <MaxFeatureCount>10000</MaxFeatureCount>
  </Database>
  
  <!-- 日志配置 -->
  <Logging>
    <!-- 是否启用日志 -->
    <EnableLogging>true</EnableLogging>
    <!-- 日志文件路径 -->
    <LogFile>D:\HookImages\Logs\HookFeature.log</LogFile>
    <!-- 日志级别：Debug, Info, Warning, Error -->
    <LogLevel>Info</LogLevel>
  </Logging>
</HookFeatureConfiguration>
