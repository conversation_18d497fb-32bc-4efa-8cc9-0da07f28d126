# 配置文件示例和说明

## 配置文件路径
配置文件必须放置在以下路径：
```
D:\LvConfig\产品型号\{modelNo}.json
```

其中`{modelNo}`是产品型号参数，例如：
- `Sample00.json` - 对应产品型号"Sample00"
- `Model-0523A.json` - 对应产品型号"Model-0523A"

## 配置文件格式

### 基本结构
配置文件采用JSON对象格式，包含元数据和检测项目列表：

```json
{
  "配置版本": "1.0",
  "更新时间": "2025-07-11 00:30:00",
  "说明": "质量检测配置参数",
  "型号名称": "产品型号",
  "片数": "3",
  "检测项目列表": [
    {
      "检测项目": "检测项名称",
      "分属相机": "相机标识",
      "严重分类": "分类代码",
      "轻微分类": "分类代码",
      "良品分类": "分类代码",
      "轻微上限": 数值,
      "良品上限": 数值,
      "良品下限": 数值,
      "轻微下限": 数值,
      "偏移量": 数值,
      "预处理模式": "字符串"
    }
  ]
}
```

### 字段说明

#### 配置元数据字段
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| 配置版本 | 字符串 | 配置文件格式版本号 | "1.0" |
| 更新时间 | 字符串 | 配置文件最后更新时间 | "2025-07-11 00:30:00" |
| 说明 | 字符串 | 配置文件说明信息 | "质量检测配置参数" |
| 型号名称 | 字符串 | 产品型号名称 | "Sample00" |
| 片数 | 字符串 | 产品片数信息 | "3" |

#### 检测项目字段
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| 检测项目 | 字符串 | 检测项的唯一标识名称，必须与输入数据中的名称完全匹配 | "外圆面伤" |
| 分属相机 | 字符串 | 检测项所属的相机标识，用于标记数据来源 | "相机1" |
| 严重分类 | 字符串 | NG2级别对应的出料分类代码 | "4" |
| 轻微分类 | 字符串 | NG1级别对应的出料分类代码 | "3" |
| 良品分类 | 字符串 | OK级别对应的出料分类代码 | "0" |
| 轻微上限 | 数值 | 轻微缺陷的上限值 | 99999.0 |
| 良品上限 | 数值 | 良品范围的上限值 | 200.0 |
| 良品下限 | 数值 | 良品范围的下限值 | 0.0 |
| 轻微下限 | 数值 | 轻微缺陷的下限值 | 200.0 |
| 偏移量 | 数值 | 测量值的校正偏移量，判定前会加到原始测量值上 | 0.0 |
| 预处理模式 | 字符串 | 预留字段，用于未来功能扩展，当前版本可为空 | "" |

## 完整示例

### 示例1：标准配置
```json
{
  "配置版本": "1.0",
  "更新时间": "2025-07-11 00:30:00",
  "说明": "质量检测配置参数",
  "型号名称": "Sample00",
  "片数": "3",
  "检测项目列表": [
    {
      "检测项目": "外圆面伤",
      "分属相机": "相机1",
      "严重分类": "4",
      "轻微分类": "3",
      "良品分类": "0",
      "轻微上限": 99999.0,
      "良品上限": 200.0,
      "良品下限": 0.0,
      "轻微下限": 200.0,
      "偏移量": 0.0,
      "预处理模式": ""
    },
    {
      "检测项目": "外圆纹路",
      "分属相机": "相机1",
      "严重分类": "4",
      "轻微分类": "3",
      "良品分类": "0",
      "轻微上限": 99999.0,
      "良品上限": 800.0,
      "良品下限": 0.0,
      "轻微下限": 800.0,
      "偏移量": 0.0,
      "预处理模式": ""
    },
    {
      "检测项目": "槽内多铜",
      "分属相机": "相机1A",
      "严重分类": "4",
      "轻微分类": "2",
      "良品分类": "0",
      "轻微上限": 99999.0,
      "良品上限": 80.0,
      "良品下限": 0.0,
      "轻微下限": 80.0,
      "偏移量": 0.0,
      "预处理模式": ""
    }
  ]
}
```

### 示例2：不同分类代码
```json
[
  {
    "检测项目": "尺寸检测",
    "分属相机": "测量相机",
    "严重分类": "9",
    "轻微分类": "7",
    "良品分类": "5",
    "轻微上限": 1000.0,
    "良品上限": 100.0,
    "良品下限": 10.0,
    "轻微下限": 5.0
  }
]
```

## 数值约束规则

### 必须满足的约束条件
1. **良品上限 ≥ 良品下限**
   - 确保良品范围有效
   
2. **轻微上限 ≥ 良品上限**
   - 轻微范围必须包含良品范围的上边界
   
3. **轻微下限 ≤ 良品下限**
   - 轻微范围必须包含良品范围的下边界

4. **分类代码唯一性**
   - 良品分类、轻微分类、严重分类的值不能相同
   
5. **分类代码范围**
   - 所有分类代码必须在'1'-'9'范围内
   - 必须是字符串类型，不能为空

### 约束验证示例

#### ✅ 正确配置
```json
{
  "轻微上限": 1000.0,  // ≥ 良品上限(100.0) ✓
  "良品上限": 100.0,   // ≥ 良品下限(10.0) ✓  
  "良品下限": 10.0,    // ≥ 轻微下限(5.0) ✓
  "轻微下限": 5.0,
  "严重分类": "9",     // 与其他分类不同 ✓
  "轻微分类": "7",     // 与其他分类不同 ✓
  "良品分类": "5"      // 与其他分类不同 ✓
}
```

#### ❌ 错误配置
```json
{
  "轻微上限": 50.0,    // < 良品上限(100.0) ❌
  "良品上限": 100.0,   
  "良品下限": 10.0,    
  "轻微下限": 20.0,    // > 良品下限(10.0) ❌
  "严重分类": "5",     
  "轻微分类": "5",     // 与严重分类相同 ❌
  "良品分类": "5"      // 与其他分类相同 ❌
}
```

## 判定范围说明

### 判定逻辑
基于配置的数值范围，判定逻辑如下：

1. **良品范围**：[良品下限, 良品上限]
   - 测量值在此范围内 → OK

2. **轻微范围**：[轻微下限, 轻微上限] - 良品范围
   - 测量值在轻微范围内但不在良品范围内 → NG1

3. **严重范围**：超出轻微范围
   - 测量值 < 轻微下限 或 测量值 > 轻微上限 → NG2

### 范围示例
假设配置为：
- 轻微下限: 0.0
- 良品下限: 10.0  
- 良品上限: 100.0
- 轻微上限: 1000.0

判定结果：
- 测量值 = 5.0 → NG1 (在轻微范围[0,1000]内，但不在良品范围[10,100]内)
- 测量值 = 50.0 → OK (在良品范围[10,100]内)
- 测量值 = 500.0 → NG1 (在轻微范围[0,1000]内，但不在良品范围[10,100]内)
- 测量值 = 1500.0 → NG2 (超出轻微范围[0,1000])

## 文件编码要求

### 重要提醒
配置文件必须使用**UTF-8编码**保存，否则会导致中文字符乱码和JSON解析失败。

### 创建配置文件的正确方法
1. 使用支持UTF-8的文本编辑器（如VS Code、Notepad++）
2. 确保保存时选择UTF-8编码
3. 避免使用Windows记事本（可能导致编码问题）

### 验证编码正确性
如果出现解析错误，可以：
1. 重新用UTF-8编码保存文件
2. 检查JSON格式是否正确
3. 确认中文字符显示正常

## 偏移量功能说明

### 偏移量的作用
偏移量用于校正测量值，在进行判定前会自动应用到原始测量值上：
```
调整后测量值 = 原始测量值 + 偏移量
```

### 使用场景
1. **传感器校准**：补偿传感器的系统性偏差
2. **基准调整**：根据标准件调整测量基准
3. **温度补偿**：补偿温度变化对测量的影响
4. **设备差异**：消除不同设备间的测量差异

### 偏移量示例

#### 示例1：正偏移量（+20.0）
```json
{
  "检测项目": "尺寸测量",
  "良品上限": 100.0,
  "良品下限": 50.0,
  "偏移量": 20.0
}
```
- 原始测量值：30.0
- 调整后测量值：30.0 + 20.0 = 50.0
- 判定结果：OK（在[50,100]范围内）

#### 示例2：负偏移量（-15.0）
```json
{
  "检测项目": "厚度测量",
  "良品上限": 80.0,
  "良品下限": 20.0,
  "偏移量": -15.0
}
```
- 原始测量值：90.0
- 调整后测量值：90.0 + (-15.0) = 75.0
- 判定结果：OK（在[20,80]范围内）

#### 示例3：零偏移量（0.0）
```json
{
  "检测项目": "标准测量",
  "偏移量": 0.0
}
```
- 调整后测量值 = 原始测量值（无变化）

### 注意事项
1. 偏移量可以是正数、负数或零
2. 偏移量在所有判定逻辑之前应用
3. 偏移量不会改变存储的原始测量值
4. 建议根据实际校准需求设置偏移量值

## 预处理模式功能说明

### 预处理模式的作用
"预处理模式"是一个预留字段，为未来功能扩展而设计。当前版本中该字段不影响判定逻辑，但会被正常加载和存储。

### 设计目的
1. **功能扩展**：为未来可能的预处理算法预留接口
2. **配置兼容**：确保配置文件格式的向前兼容性
3. **系统升级**：支持渐进式功能升级而不破坏现有配置

### 当前状态
- **字段类型**：字符串
- **默认值**：空字符串 ""
- **功能状态**：预留，不参与当前判定逻辑
- **加载状态**：正常加载并可通过API访问

## 预处理模式功能说明

### 预处理模式的作用
"预处理模式"是一个预留字段，为未来功能扩展而设计。当前版本中该字段不影响判定逻辑，但会被正常加载和存储。

### 设计目的
1. **功能扩展**：为未来可能的预处理算法预留接口
2. **配置兼容**：确保配置文件格式的向前兼容性
3. **系统升级**：支持渐进式功能升级而不破坏现有配置

### 当前状态
- **字段类型**：字符串
- **默认值**：空字符串 ""
- **功能状态**：预留，不参与当前判定逻辑
- **加载状态**：正常加载并可通过API访问

### 预处理模式示例
```json
{
  "检测项目": "外圆面伤",
  "预处理模式": ""           // 当前版本保持为空
}
```

### 未来可能的应用场景
1. **图像预处理**：指定不同的图像处理算法
2. **数据滤波**：应用特定的数据滤波模式
3. **算法选择**：选择不同的检测算法
4. **参数调优**：指定特定的参数优化策略

### 使用建议
1. 当前版本建议保持为空字符串
2. 为未来升级预留配置空间
3. 不要在当前版本中依赖此字段的值
