using System;
using System.Drawing;
using System.Runtime.InteropServices;
using Emgu.CV;
using Emgu.CV.CvEnum;
using Emgu.CV.Structure;

public static class ImageRecognitionHelper
{
    [DllImport("user32.dll")]
    private static extern void mouse_event(int dwFlags, int dx, int dy, int cButtons, int dwExtraInfo);

    private const int MOUSEEVENTF_LEFTDOWN = 0x02;
    private const int MOUSEEVENTF_LEFTUP = 0x04;
    private const int MOUSEEVENTF_MOVE = 0x01;

    /// <summary>
    /// 在屏幕中查找目标图片并返回匹配位置
    /// </summary>
    /// <param name="templatePath">目标图片路径</param>
    /// <param name="threshold">匹配阈值（0-1）</param>
    /// <returns>匹配的矩形区域，未找到返回空</returns>
    public static Rectangle? FindImageOnScreen(string templatePath, double threshold = 0.8)
    {
        // 获取屏幕截图
        using (Bitmap screenShot = CaptureScreen())
        using (Image<Bgr, byte> screenImage = screenShot.ToImage<Bgr, byte>())
        using (Image<Bgr, byte> templateImage = new Image<Bgr, byte>(templatePath))
        {
            Image<Gray, float> result = screenImage.MatchTemplate(templateImage, TemplateMatchingType.CcoeffNormed);
            CvInvoke.Normalize(result, result, 0, 1, NormType.MinMax);

            double minVal, maxVal;
            Point minLoc, maxLoc;
            CvInvoke.MinMaxLoc(result, out minVal, out maxVal, out minLoc, out maxLoc);

            if (maxVal >= threshold)
            {
                return new Rectangle(maxLoc, templateImage.Size);
            }
            return null;
        }
    }

    /// <summary>
    /// 模拟鼠标双击指定坐标
    /// </summary>
    /// <param name="x">屏幕X坐标</param>
    /// <param name="y">屏幕Y坐标</param>
    public static void DoubleClickAt(int x, int y)
    {
        // 移动鼠标到目标位置
        SetCursorPos(x, y);
        
        // 第一次单击
        mouse_event(MOUSEEVENTF_LEFTDOWN, x, y, 0, 0);
        mouse_event(MOUSEEVENTF_LEFTUP, x, y, 0, 0);
        
        // 第二次单击（间隔50ms模拟真实双击）
        System.Threading.Thread.Sleep(50);
        mouse_event(MOUSEEVENTF_LEFTDOWN, x, y, 0, 0);
        mouse_event(MOUSEEVENTF_LEFTUP, x, y, 0, 0);
    }

    [DllImport("user32.dll")]
    private static extern bool SetCursorPos(int X, int Y);

    private static Bitmap CaptureScreen()
    {
        Rectangle screenBounds = Screen.PrimaryScreen.Bounds;
        Bitmap bmp = new Bitmap(screenBounds.Width, screenBounds.Height);
        using (Graphics g = Graphics.FromImage(bmp))
        {
            g.CopyFromScreen(screenBounds.X, screenBounds.Y, 0, 0, screenBounds.Size);
        }
        return bmp;
    }
}
