Jud4Script v2.0.0 快速开始

1. 环境要求:
   - .NET Framework 4.0 或更高版本
   - Windows 操作系统

2. 安装步骤:
   - 将 核心文件\Jud4Script.dll 和 Newtonsoft.Json.dll 添加到项目引用
   - 将 配置文件\0523A-F.json 复制到 D:\LvConfig\0523A-F\ 目录
   - 参考 示例代码\ 目录中的示例开始使用

3. 基本使用:
   string result = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7");

4. 详细文档:
   - 文档\安装包说明.md
   - 文档\API文档.md
   - 文档\配置说明.md

5. 测试验证:
   - 运行 测试程序\TestSingleValueConstraints.exe 验证约束功能
   - 运行 测试程序\TestExceptionHandling.exe 验证异常处理

6. 示例代码:
   - 示例代码\BasicUsage.cs - 基本使用示例
   - 示例代码\AdvancedUsage.cs - 高级使用示例
   - 示例代码\DebugExample.cs - 调试功能示例

7. 输入格式:
   正确格式:
   - "Items=;外圆面伤=180.0"                    # 单值
   - "Items=;槽宽*3=0.32,0.31,0.35"            # 多值
   
   错误格式:
   - "Items=;外圆面伤*1=180.0"                 # 单值不允许*1
   - "Items=;外圆面伤=185,179"                 # 单值不允许多个测量值

8. 返回值:
   - "1"-"9": 出料分类（良品、轻微、严重对应不同出料口）
   - "0": 异常分类（需要人工处理）

9. 调试功能:
   JudgmentEngine.SetDebugEnabled(true);  // 启用调试
   string debugInfo = JudgmentEngine.GetDebugInfo();  // 获取调试信息

10. 技术支持:
    如有问题请查看调试信息定位问题，或联系开发团队获取支持。
