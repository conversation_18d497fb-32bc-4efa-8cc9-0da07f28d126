# Jud4Script API 文档

## 📋 API 概述

Jud4Script提供简洁而强大的API接口，支持产品检测判定、调试信息获取和配置管理。

## 🔧 主要接口

### JudgmentEngine 类

#### 1. Evaluate 方法
**功能**：执行产品检测判定

```csharp
public static string Evaluate(string modelNo, string inspecInfo)
```

**参数**：
- `modelNo` (string)：产品型号，用于查找对应的配置文件
- `inspecInfo` (string)：检测信息字符串，包含检测项和测量值

**返回值**：
- `string`：判定结果
  - `"1"-"9"`：出料分类（良品、轻微、严重对应不同出料口）
  - `"0"`：异常分类（ERROR）

**示例**：
```csharp
// 单值判定
string result1 = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7");

// 多值判定
string result2 = JudgmentEngine.Evaluate("0523A-F", "Items=;槽宽*3=0.32,0.31,0.35");

// 混合判定
string result3 = JudgmentEngine.Evaluate("0523A-F", 
    "CAM=C5;TS=;PD=OK;POF=;Items=;槽深=0.7;槽宽*3=0.32,0.31,0.35");
```

#### 2. GetDebugInfo 方法
**功能**：获取最近一次判定的详细调试信息

```csharp
public static string GetDebugInfo()
```

**返回值**：
- `string`：包含时间戳、处理步骤、参数信息的详细调试日志

**示例**：
```csharp
JudgmentEngine.SetDebugEnabled(true);
string result = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7");
string debugInfo = JudgmentEngine.GetDebugInfo();
Console.WriteLine(debugInfo);
```

#### 3. ClearDebugInfo 方法
**功能**：清空调试信息缓存

```csharp
public static void ClearDebugInfo()
```

**示例**：
```csharp
JudgmentEngine.ClearDebugInfo();
```

#### 4. SetDebugEnabled 方法
**功能**：启用或禁用调试模式

```csharp
public static void SetDebugEnabled(bool enabled)
```

**参数**：
- `enabled` (bool)：是否启用调试模式

**示例**：
```csharp
// 启用调试模式
JudgmentEngine.SetDebugEnabled(true);

// 禁用调试模式
JudgmentEngine.SetDebugEnabled(false);
```

## 📊 数据结构

### InspecItemConfig 类
**功能**：检测项配置信息

```csharp
public class InspecItemConfig
{
    public string Camera { get; set; }        // 相机编号
    public float GoodLower { get; set; }      // 良品下限
    public float GoodUpper { get; set; }      // 良品上限
    public float LightLower { get; set; }     // 轻微下限
    public float LightUpper { get; set; }     // 轻微上限
    public float Offset { get; set; }         // 偏移量
    public string GoodClass { get; set; }     // 良品分类
    public string LightClass { get; set; }    // 轻微分类
    public string SevereClass { get; set; }   // 严重分类
    public string 预处理模式 { get; set; }    // 预处理模式（预留）
}
```

## 🔍 输入格式详解

### 基本格式
```
"CAM=相机编号;TS=时间戳;PD=预判定;POF=;Items=;检测项1=测量值1;检测项2*n=测量值1,测量值2,...;..."
```

### 字段说明
- **CAM**：相机编号（可选）
- **TS**：时间戳（可选）
- **PD**：预判定结果（可选）
- **POF**：预留字段（可选）
- **Items**：检测项开始标识（必需）
- **检测项**：具体的检测项名称和测量值

### 检测项格式
1. **单值格式**：`检测项名称=测量值`
   ```
   外圆面伤=180.0
   槽深=0.7
   ```

2. **多值格式**：`检测项名称*数量=值1,值2,...`
   ```
   槽宽*3=0.32,0.31,0.35
   槽深*5=0.746,0.713,0.715,0.719,0.732
   ```

### 格式约束
- 单值情况不允许使用`*n`格式
- 单值情况不允许多个测量值
- 多值情况必须使用`*n`格式且数量一致
- 测量值必须为有效数字

## 📈 判定逻辑

### 判定流程
1. **配置加载**：根据产品型号加载配置文件
2. **输入解析**：解析检测信息字符串
3. **格式验证**：验证输入格式和约束规则
4. **数量验证**：验证多值情况的数量一致性
5. **测量值判定**：对每个测量值进行判定
6. **最差结果选择**：多值情况选择最差判定结果
7. **最终分类**：确定最终的出料分类

### 判定规则
对于每个测量值：
1. 应用偏移量：`调整值 = 测量值 + 偏移量`
2. 判定逻辑：
   - 如果 `调整值` 在 `[GoodLower, GoodUpper]` 范围内 → **OK**
   - 如果 `调整值` 在 `[LightLower, LightUpper]` 范围内 → **NG1**
   - 否则 → **NG2**

### 最终分类
- 所有检测项都是 **OK** → 返回 `GoodClass`
- 有检测项是 **NG1**，无 **NG2** → 返回 `LightClass`
- 有检测项是 **NG2** → 返回 `SevereClass`
- 有检测项是 **ERROR** → 返回 `"0"`

## 🚨 异常处理

### 异常类型
1. **配置加载异常**
   - 产品型号不存在
   - 配置文件格式错误
   - 配置参数无效

2. **输入解析异常**
   - 输入格式错误
   - Items字段缺失
   - 检测项格式错误

3. **格式约束异常**
   - 单值使用*n格式
   - 单值包含多个测量值
   - 数量不一致

4. **数值解析异常**
   - 测量值不是有效数字
   - 测量值为空

### 错误信息格式
```
[ERROR] 错误位置 - 错误类型: 详细描述
```

示例：
```
[ERROR] 配置加载 - 配置文件为空: 产品型号 'INVALID-MODEL' 对应的配置文件不存在或为空
[ERROR] 测量值解析 - 解析失败: 检测项 '槽深' 的测量值解析失败: 单值情况不允许多个测量值
```

## 🔧 配置管理

### 配置文件位置
- **主路径**：`D:\LvConfig\产品型号\{产品型号}.json`
- **备用路径**：当前目录下的 `{产品型号}.json`

### 配置文件格式
```json
{
  "检测项名称": {
    "Camera": "相机5",
    "GoodLower": 0.6,
    "GoodUpper": 0.8,
    "LightLower": 0.5,
    "LightUpper": 0.6,
    "Offset": 0.0,
    "GoodClass": "1",
    "LightClass": "5",
    "SevereClass": "9",
    "预处理模式": ""
  }
}
```

## 💡 最佳实践

### 1. 错误处理
```csharp
try
{
    string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);
    if (result == "0")
    {
        // 处理异常情况
        string debugInfo = JudgmentEngine.GetDebugInfo();
        Console.WriteLine("判定异常: " + debugInfo);
    }
}
catch (Exception ex)
{
    Console.WriteLine("系统异常: " + ex.Message);
}
```

### 2. 调试模式使用
```csharp
// 开发阶段启用调试
#if DEBUG
    JudgmentEngine.SetDebugEnabled(true);
#endif

string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);

#if DEBUG
    Console.WriteLine(JudgmentEngine.GetDebugInfo());
#endif
```

### 3. 性能优化
- 配置文件会自动缓存，无需担心重复加载
- 调试模式会影响性能，生产环境建议禁用
- 大量调用时建议定期清空调试信息

## 📞 技术支持

如有API使用问题，请：
1. 查看调试信息定位问题
2. 参考示例代码和测试程序
3. 检查输入格式和配置文件
4. 联系开发团队获取支持
