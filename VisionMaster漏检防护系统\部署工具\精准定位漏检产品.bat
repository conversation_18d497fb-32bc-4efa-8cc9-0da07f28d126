@echo off
chcp 65001 >nul
echo ========================================
echo 🎯 精准定位漏检产品工具
echo ========================================
echo.
echo 此工具将自动分析检测历史，精准定位可能的漏检产品
echo 无需手动逐个查找，系统会自动排序并优先显示最可疑的产品
echo.

:: 检查是否存在检测日志
if not exist "D:\HODD\DetectionLog.txt" (
    echo ❌ 错误: 未找到检测日志文件
    echo    请确保已运行过检测系统并生成了日志数据
    echo    日志路径: D:\HODD\DetectionLog.txt
    echo.
    pause
    exit /b 1
)

:: 检查日志文件是否有数据
for /f %%i in ('type "D:\HODD\DetectionLog.txt" ^| find /c /v ""') do set linecount=%%i
if %linecount% LEQ 1 (
    echo ⚠️  警告: 检测日志文件为空或只有表头
    echo    请确保已有足够的检测数据进行分析
    echo    当前行数: %linecount%
    echo.
    pause
    exit /b 1
)

echo ✓ 检测到 %linecount% 行检测数据
echo.

:: 检查分析工具是否存在
if not exist "D:\HODD\ImageAnalyzer.exe" (
    echo 📦 分析工具不存在，正在编译...
    echo.

    :: 调用编译脚本
    call "%~dp0编译分析工具.bat"

    :: 检查编译是否成功
    if not exist "D:\HODD\ImageAnalyzer.exe" (
        echo ❌ 编译失败，无法继续
        echo    请手动运行编译脚本解决问题
        pause
        exit /b 1
    )

    echo ✓ 编译成功
    echo.
)

echo 🚀 启动精准定位分析...
echo.
echo 分析模式说明：
echo 1. 智能漏检精准定位 - 使用严格算法快速找到最可疑的产品
echo 2. 全面历史分析 - 分析所有历史数据，生成完整报告
echo 3. 指定时间段分析 - 分析特定时间范围内的数据
echo.
echo 推荐使用模式1进行快速精准定位
echo.

:: 切换到工作目录
cd /d "D:\HODD"

:: 运行分析工具
echo 正在启动分析工具...
echo.
ImageAnalyzer.exe

echo.
echo ========================================
echo 分析完成！
echo ========================================
echo.

:: 检查生成的报告文件
if exist "D:\HODD\AnalysisResult_精准定位报告.txt" (
    echo 📋 已生成精准定位报告
    echo.
    echo 是否立即查看精准定位报告？(Y/N)
    set /p viewreport=
    if /i "%viewreport%"=="Y" (
        start "" "D:\HODD\AnalysisResult_精准定位报告.txt"
    )
)

if exist "D:\HODD\AnalysisResult_图像对比.bat" (
    echo.
    echo 🖼️  已生成图像对比脚本
    echo.
    echo 是否立即运行图像对比脚本？(Y/N)
    set /p runcompare=
    if /i "%runcompare%"=="Y" (
        call "D:\HODD\AnalysisResult_图像对比.bat"
    )
)

if exist "D:\HODD\AnalysisResult_详细dis分析.txt" (
    echo.
    echo 📊 已生成详细dis参数分析
    echo.
    echo 是否查看详细dis分析？(Y/N)
    set /p viewdis=
    if /i "%viewdis%"=="Y" (
        start "" "D:\HODD\AnalysisResult_详细dis分析.txt"
    )
)

echo.
echo 📁 所有分析结果已保存到: D:\HODD\
echo.
echo 生成的文件：
if exist "D:\HODD\AnalysisResult.txt" echo ✓ AnalysisResult.txt - 基础分析报告
if exist "D:\HODD\AnalysisResult_精准定位报告.txt" echo ✓ AnalysisResult_精准定位报告.txt - 精准定位报告
if exist "D:\HODD\AnalysisResult_图像对比.bat" echo ✓ AnalysisResult_图像对比.bat - 图像对比脚本
if exist "D:\HODD\AnalysisResult_详细dis分析.txt" echo ✓ AnalysisResult_详细dis分析.txt - 详细参数分析
echo.

echo 🎯 使用建议：
echo 1. 优先查看精准定位报告中🔴标记的产品
echo 2. 使用图像对比脚本快速验证疑似漏检
echo 3. 查看详细dis分析了解参数差异
echo 4. 根据分析结果调整检测阈值
echo.

echo 如需重新分析，请再次运行此脚本
echo.
pause
