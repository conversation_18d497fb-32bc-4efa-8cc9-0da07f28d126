using System;
using Jud4Script;

/// <summary>
/// Jud4Script 高级使用示例
/// </summary>
class AdvancedUsage
{
    static void Main()
    {
        Console.WriteLine("=== Jud4Script 高级使用示例 ===");
        
        // 示例1：批量处理
        Console.WriteLine("示例1 - 批量处理:");
        string[] testData = {
            "Items=;槽深=0.7;槽宽=0.4",
            "Items=;槽深=0.65;槽宽*2=0.35,0.36",
            "Items=;槽深*3=0.7,0.72,0.75;槽宽=0.4",
            "Items=;槽深=0.55;槽宽=0.45"
        };
        
        for (int i = 0; i < testData.Length; i++)
        {
            string result = JudgmentEngine.Evaluate("0523A-F", testData[i]);
            Console.WriteLine($"测试{i+1}: {result} <- {testData[i]}");
        }
        Console.WriteLine();
        
        // 示例2：错误处理
        Console.WriteLine("示例2 - 错误处理:");
        try
        {
            // 正常情况
            string normalResult = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7");
            Console.WriteLine("正常判定: " + normalResult);
            
            // 异常情况1：不存在的产品型号
            string errorResult1 = JudgmentEngine.Evaluate("INVALID-MODEL", "Items=;槽深=0.7");
            Console.WriteLine("无效型号: " + errorResult1);
            
            // 异常情况2：格式错误
            string errorResult2 = JudgmentEngine.Evaluate("0523A-F", "InvalidFormat");
            Console.WriteLine("格式错误: " + errorResult2);
            
            // 异常情况3：单值多测量值
            string errorResult3 = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7,0.72");
            Console.WriteLine("格式约束错误: " + errorResult3);
        }
        catch (Exception ex)
        {
            Console.WriteLine("系统异常: " + ex.Message);
        }
        Console.WriteLine();
        
        // 示例3：性能测试
        Console.WriteLine("示例3 - 性能测试:");
        int testCount = 1000;
        DateTime startTime = DateTime.Now;
        
        for (int i = 0; i < testCount; i++)
        {
            JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7;槽宽=0.4");
        }
        
        DateTime endTime = DateTime.Now;
        TimeSpan elapsed = endTime - startTime;
        double avgTime = elapsed.TotalMilliseconds / testCount;
        
        Console.WriteLine($"执行{testCount}次判定");
        Console.WriteLine($"总耗时: {elapsed.TotalMilliseconds:F2} ms");
        Console.WriteLine($"平均耗时: {avgTime:F3} ms/次");
        Console.WriteLine($"处理速度: {testCount / elapsed.TotalSeconds:F0} 次/秒");
        Console.WriteLine();
        
        // 示例4：多产品型号处理
        Console.WriteLine("示例4 - 多产品型号处理:");
        string[] modelNumbers = { "0523A-F" }; // 可以添加更多型号
        
        foreach (string model in modelNumbers)
        {
            Console.WriteLine($"处理产品型号: {model}");
            try
            {
                string result = JudgmentEngine.Evaluate(model, "Items=;槽深=0.7");
                Console.WriteLine($"  判定结果: {result}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  处理失败: {ex.Message}");
            }
        }
        Console.WriteLine();
        
        // 示例5：复杂多值场景
        Console.WriteLine("示例5 - 复杂多值场景:");
        
        // 真实VisionMaster数据格式
        string realWorldData = "CAM=C5;TS=;PD=OK;POF=;Items=;" +
                              "槽深*5=0.746,0.713,0.715,0.719,0.732;" +
                              "槽宽*5=0.434,0.436,0.440,0.436,0.435;" +
                              "槽槽夹角*5=-169.502,-97.196,-25.875,45.888,118.184";
        
        string complexResult = JudgmentEngine.Evaluate("0523A-F", realWorldData);
        Console.WriteLine("复杂多值数据:");
        Console.WriteLine("输入: " + realWorldData);
        Console.WriteLine("输出: " + complexResult);
        Console.WriteLine();
        
        // 示例6：结果统计
        Console.WriteLine("示例6 - 结果统计:");
        string[] testResults = new string[100];
        int[] resultCounts = new int[10]; // 0-9的计数
        
        Random rand = new Random();
        for (int i = 0; i < testResults.Length; i++)
        {
            // 生成随机测量值
            double randomValue = 0.5 + rand.NextDouble() * 0.4; // 0.5-0.9范围
            string testInput = $"Items=;槽深={randomValue:F3}";
            
            string result = JudgmentEngine.Evaluate("0523A-F", testInput);
            testResults[i] = result;
            
            if (int.TryParse(result, out int resultIndex))
            {
                resultCounts[resultIndex]++;
            }
        }
        
        Console.WriteLine($"随机测试{testResults.Length}次的结果统计:");
        for (int i = 0; i < resultCounts.Length; i++)
        {
            if (resultCounts[i] > 0)
            {
                double percentage = (double)resultCounts[i] / testResults.Length * 100;
                Console.WriteLine($"  分类{i}: {resultCounts[i]}次 ({percentage:F1}%)");
            }
        }
        Console.WriteLine();
        
        // 示例7：配置验证
        Console.WriteLine("示例7 - 配置验证:");
        Console.WriteLine("验证配置文件是否正确加载...");
        
        // 通过判定一个已知值来验证配置
        string knownGoodResult = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7");
        string knownBadResult = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.1");
        
        Console.WriteLine($"已知良品值(0.7)判定结果: {knownGoodResult}");
        Console.WriteLine($"已知不良值(0.1)判定结果: {knownBadResult}");
        
        if (knownGoodResult != "0" && knownBadResult != "0")
        {
            Console.WriteLine("✓ 配置文件加载正常");
        }
        else
        {
            Console.WriteLine("✗ 配置文件可能有问题");
        }
        
        Console.WriteLine();
        Console.WriteLine("=== 高级使用示例完成 ===");
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}
