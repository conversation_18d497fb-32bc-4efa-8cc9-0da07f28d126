﻿using System;
using System.Text;
using System.Windows.Forms;
using Script.Methods;
/************************************
Shell Module default code: using .NET Framwwork 4.6.1
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;  
    
	double pix2mmC4;			    /// C1像素比例
	float abs_slot_offset;			/// 对槽偏移量绝对值
    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;
        
        pix2mmC4 = 37.384;

    }

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
        //You can add your codes here, for realizing your desired function
		//每次执行将进入该函数，此处添加所需的逻辑流程处理
                
        abs_slot_offset = Math.Abs(slot_offset);
        int radius_pix = (int)(dia * pix2mmC4 * 0.5);
        double angleInRadians = Math.Asin(slot_offset / radius_pix);
        double degrees = angleInRadians * (180 / Math.PI);
        
        if((vertAngle >= -90) && (vertAngle < 90))
        {
        	pul_offset = (float)(degrees / 360 * spr);
        }
        else
	    {
        	float pul_perslot = spr / slotnum;
        	pul_offset = 2*pul_perslot - ((float)(degrees / 360 * spr));
	    }

        return true;
    }
}
                            