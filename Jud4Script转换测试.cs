using System;
using System.Text;
using System.IO;

/// <summary>
/// Jud4Script参数转换功能测试程序
/// 用于验证in0参数到inspecInfo格式的转换是否正确
/// </summary>
public class Jud4ScriptConversionTest
{
    public static void Main(string[] args)
    {
        Console.WriteLine("===========================================");
        Console.WriteLine("    Jud4Script 参数转换功能测试");
        Console.WriteLine("===========================================");
        Console.WriteLine();

        // 创建测试实例
        var tester = new Jud4ScriptConversionTest();
        
        // 运行各种格式的测试
        tester.RunAllTests();
        
        Console.WriteLine("===========================================");
        Console.WriteLine("测试完成！按任意键退出...");
        Console.ReadKey();
    }
    
    public void RunAllTests()
    {
        Console.WriteLine("1. 测试单个数值格式");
        TestSingleNumericValue();
        Console.WriteLine();
        
        Console.WriteLine("2. 测试键值对格式");
        TestKeyValueFormat();
        Console.WriteLine();
        
        Console.WriteLine("3. 测试逗号分隔格式");
        TestCommaDelimitedFormat();
        Console.WriteLine();
        
        Console.WriteLine("4. 测试分号分隔格式");
        TestSemicolonDelimitedFormat();
        Console.WriteLine();
        
        Console.WriteLine("5. 测试异常情况");
        TestExceptionCases();
        Console.WriteLine();
    }
    
    private void TestSingleNumericValue()
    {
        string[] testCases = {
            "1.250",
            "0.000",
            "2.500",
            "0.750"
        };

        foreach (string testCase in testCases)
        {
            string result = ConvertIn0ToInspecInfo(testCase);
            Console.WriteLine("  输入: " + testCase);
            Console.WriteLine("  输出: " + result);
            // 单个数值默认对应外圆碰伤
            Console.WriteLine("  验证: " + (result.Contains("外圆碰伤=" + testCase + ";") ? "✅" : "❌"));
            Console.WriteLine();
        }
    }
    
    private void TestKeyValueFormat()
    {
        string[] testCases = {
            "外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000",
            "外圆碰伤=2.000;外圆纹路=1.000",
            "槽深异常=0.750;槽宽异常=0.450;表面划伤=0.200"
        };

        foreach (string testCase in testCases)
        {
            string result = ConvertIn0ToInspecInfo(testCase);
            Console.WriteLine("  输入: " + testCase);
            Console.WriteLine("  输出: " + result);
            Console.WriteLine("  验证: " + (result.Contains("CAM=C1;TS=;JD=;;Items=;") ? "✅" : "❌"));
            Console.WriteLine();
        }
    }
    
    private void TestCommaDelimitedFormat()
    {
        string[] testCases = {
            "1.250,0.500,0.000",
            "2.000,1.000,0.200,0.750",
            "0.000,0.000"
        };

        string[] expectedDefects = {"外圆碰伤", "外圆纹路", "槽内多铜", "槽深异常", "槽宽异常", "表面划伤", "尺寸偏差", "形状变形"};

        foreach (string testCase in testCases)
        {
            string result = ConvertIn0ToInspecInfo(testCase);
            Console.WriteLine("  输入: " + testCase);
            Console.WriteLine("  输出: " + result);

            // 验证是否包含正确的缺陷项目
            string[] values = testCase.Split(',');
            bool isValid = true;
            for (int i = 0; i < values.Length && i < expectedDefects.Length; i++)
            {
                string expectedPattern = expectedDefects[i] + "=" + values[i].Trim() + ";";
                if (!result.Contains(expectedPattern))
                {
                    isValid = false;
                    break;
                }
            }
            Console.WriteLine("  验证: " + (isValid ? "✅" : "❌"));
            Console.WriteLine();
        }
    }
    
    private void TestSemicolonDelimitedFormat()
    {
        string[] testCases = {
            "180.0;0.0;0.0",
            "100.5;200.3;300.7",
            "50.0;60.0;70.0;80.0"
        };
        
        foreach (string testCase in testCases)
        {
            string result = ConvertIn0ToInspecInfo(testCase);
            Console.WriteLine($"  输入: {testCase}");
            Console.WriteLine($"  输出: {result}");
            
            // 验证是否包含正确的检测项目
            string[] values = testCase.Split(';');
            bool isValid = true;
            for (int i = 0; i < values.Length; i++)
            {
                string value = values[i].Trim();
                if (!string.IsNullOrWhiteSpace(value) && !result.Contains($"检测项目{i + 1}={value};"))
                {
                    isValid = false;
                    break;
                }
            }
            Console.WriteLine($"  验证: {(isValid ? "✅" : "❌")}");
            Console.WriteLine();
        }
    }
    
    private void TestExceptionCases()
    {
        string[] testCases = {
            "",
            null,
            "无效格式",
            "项目名=",
            "=100.0",
            "项目1=值1;项目2="
        };
        
        foreach (string testCase in testCases)
        {
            try
            {
                string result = ConvertIn0ToInspecInfo(testCase);
                Console.WriteLine($"  输入: {testCase ?? "null"}");
                Console.WriteLine($"  输出: {result}");
                Console.WriteLine($"  验证: {(result.StartsWith("CAM=C1;TS=;JD=;;Items=;") ? "✅" : "❌")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  输入: {testCase ?? "null"}");
                Console.WriteLine($"  异常: {ex.Message}");
                Console.WriteLine($"  验证: ❌");
            }
            Console.WriteLine();
        }
    }
    
    // 复制转换函数用于测试
    private string ConvertIn0ToInspecInfo(string in0Value)
    {
        if (string.IsNullOrEmpty(in0Value))
        {
            return "CAM=C1;TS=;JD=;;Items=;";
        }

        StringBuilder inspecInfoBuilder = new StringBuilder();
        inspecInfoBuilder.Append("CAM=C1;TS=;JD=;;Items=;");

        try
        {
            if (in0Value.Contains("="))
            {
                ProcessDefectKeyValueFormat(in0Value, inspecInfoBuilder);
            }
            else if (in0Value.Contains(","))
            {
                ProcessDefectCommaDelimitedFormat(in0Value, inspecInfoBuilder);
            }
            else if (in0Value.Contains(";"))
            {
                ProcessDefectSemicolonDelimitedFormat(in0Value, inspecInfoBuilder);
            }
            else if (IsNumericValue(in0Value))
            {
                inspecInfoBuilder.Append("外圆碰伤=" + in0Value + ";");
            }
            else
            {
                inspecInfoBuilder.Append("外圆碰伤=" + in0Value + ";");
            }
        }
        catch (Exception)
        {
            return "CAM=C1;TS=;JD=;;Items=;外圆碰伤=0.000;";
        }

        return inspecInfoBuilder.ToString();
    }

    // 缺陷项目名称映射
    private string[] GetDefectItemNames()
    {
        return new string[]
        {
            "外圆碰伤", "外圆纹路", "槽内多铜", "槽深异常",
            "槽宽异常", "表面划伤", "尺寸偏差", "形状变形"
        };
    }
    
    private void ProcessDefectKeyValueFormat(string input, StringBuilder builder)
    {
        string[] pairs = input.Split(';');
        foreach (string pair in pairs)
        {
            if (!string.IsNullOrWhiteSpace(pair) && pair.Contains("="))
            {
                string cleanPair = pair.Trim();
                string[] keyValue = cleanPair.Split('=');
                if (keyValue.Length == 2)
                {
                    string defectName = keyValue[0].Trim();
                    string defectValue = keyValue[1].Trim();

                    if (IsNumericValue(defectValue))
                    {
                        builder.Append(defectName + "=" + defectValue + ";");
                    }
                    else
                    {
                        builder.Append(defectName + "=0.000;");
                    }
                }
            }
        }
    }

    private void ProcessDefectCommaDelimitedFormat(string input, StringBuilder builder)
    {
        string[] values = input.Split(',');
        string[] defectNames = GetDefectItemNames();

        for (int i = 0; i < values.Length && i < defectNames.Length; i++)
        {
            string value = values[i].Trim();
            if (!string.IsNullOrWhiteSpace(value))
            {
                if (IsNumericValue(value))
                {
                    builder.Append(defectNames[i] + "=" + value + ";");
                }
                else
                {
                    builder.Append(defectNames[i] + "=0.000;");
                }
            }
        }
    }

    private void ProcessDefectSemicolonDelimitedFormat(string input, StringBuilder builder)
    {
        string[] values = input.Split(';');
        string[] defectNames = GetDefectItemNames();
        int itemIndex = 0;

        foreach (string value in values)
        {
            string cleanValue = value.Trim();
            if (!string.IsNullOrWhiteSpace(cleanValue) && itemIndex < defectNames.Length)
            {
                if (IsNumericValue(cleanValue))
                {
                    builder.Append(defectNames[itemIndex] + "=" + cleanValue + ";");
                }
                else
                {
                    builder.Append(defectNames[itemIndex] + "=0.000;");
                }
                itemIndex++;
            }
        }
    }
    
    private bool IsNumericValue(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return false;
            
        double result;
        return double.TryParse(value, out result);
    }
}
