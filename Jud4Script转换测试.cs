using System;
using System.Text;
using System.IO;

/// <summary>
/// Jud4Script参数转换功能测试程序
/// 用于验证in0参数到inspecInfo格式的转换是否正确
/// </summary>
public class Jud4ScriptConversionTest
{
    public static void Main(string[] args)
    {
        Console.WriteLine("===========================================");
        Console.WriteLine("    Jud4Script 参数转换功能测试");
        Console.WriteLine("===========================================");
        Console.WriteLine();

        // 创建测试实例
        var tester = new Jud4ScriptConversionTest();
        
        // 运行各种格式的测试
        tester.RunAllTests();
        
        Console.WriteLine("===========================================");
        Console.WriteLine("测试完成！按任意键退出...");
        Console.ReadKey();
    }
    
    public void RunAllTests()
    {
        Console.WriteLine("1. 测试单个数值格式");
        TestSingleNumericValue();
        Console.WriteLine();
        
        Console.WriteLine("2. 测试键值对格式");
        TestKeyValueFormat();
        Console.WriteLine();
        
        Console.WriteLine("3. 测试逗号分隔格式");
        TestCommaDelimitedFormat();
        Console.WriteLine();
        
        Console.WriteLine("4. 测试分号分隔格式");
        TestSemicolonDelimitedFormat();
        Console.WriteLine();
        
        Console.WriteLine("5. 测试异常情况");
        TestExceptionCases();
        Console.WriteLine();
    }
    
    private void TestSingleNumericValue()
    {
        string[] testCases = {
            "180.5",
            "0.0",
            "999.999",
            "-50.5"
        };
        
        foreach (string testCase in testCases)
        {
            string result = ConvertIn0ToInspecInfo(testCase);
            Console.WriteLine($"  输入: {testCase}");
            Console.WriteLine($"  输出: {result}");
            Console.WriteLine($"  验证: {(result.Contains($"检测项目1={testCase};") ? "✅" : "❌")}");
            Console.WriteLine();
        }
    }
    
    private void TestKeyValueFormat()
    {
        string[] testCases = {
            "外圆面伤=180.0;外圆纹路=0.0;槽内多铜=0.0",
            "槽深=0.75;槽宽=0.45",
            "检测项目1=100.0;检测项目2=200.0;检测项目3=300.0"
        };
        
        foreach (string testCase in testCases)
        {
            string result = ConvertIn0ToInspecInfo(testCase);
            Console.WriteLine($"  输入: {testCase}");
            Console.WriteLine($"  输出: {result}");
            Console.WriteLine($"  验证: {(result.Contains("CAM=C1;TS=;JD=;;Items=;") ? "✅" : "❌")}");
            Console.WriteLine();
        }
    }
    
    private void TestCommaDelimitedFormat()
    {
        string[] testCases = {
            "180.0,0.0,0.0",
            "100.5,200.3,300.7,400.1",
            "50.0,60.0"
        };
        
        foreach (string testCase in testCases)
        {
            string result = ConvertIn0ToInspecInfo(testCase);
            Console.WriteLine($"  输入: {testCase}");
            Console.WriteLine($"  输出: {result}");
            
            // 验证是否包含正确的检测项目
            string[] values = testCase.Split(',');
            bool isValid = true;
            for (int i = 0; i < values.Length; i++)
            {
                if (!result.Contains($"检测项目{i + 1}={values[i].Trim()};"))
                {
                    isValid = false;
                    break;
                }
            }
            Console.WriteLine($"  验证: {(isValid ? "✅" : "❌")}");
            Console.WriteLine();
        }
    }
    
    private void TestSemicolonDelimitedFormat()
    {
        string[] testCases = {
            "180.0;0.0;0.0",
            "100.5;200.3;300.7",
            "50.0;60.0;70.0;80.0"
        };
        
        foreach (string testCase in testCases)
        {
            string result = ConvertIn0ToInspecInfo(testCase);
            Console.WriteLine($"  输入: {testCase}");
            Console.WriteLine($"  输出: {result}");
            
            // 验证是否包含正确的检测项目
            string[] values = testCase.Split(';');
            bool isValid = true;
            for (int i = 0; i < values.Length; i++)
            {
                string value = values[i].Trim();
                if (!string.IsNullOrWhiteSpace(value) && !result.Contains($"检测项目{i + 1}={value};"))
                {
                    isValid = false;
                    break;
                }
            }
            Console.WriteLine($"  验证: {(isValid ? "✅" : "❌")}");
            Console.WriteLine();
        }
    }
    
    private void TestExceptionCases()
    {
        string[] testCases = {
            "",
            null,
            "无效格式",
            "项目名=",
            "=100.0",
            "项目1=值1;项目2="
        };
        
        foreach (string testCase in testCases)
        {
            try
            {
                string result = ConvertIn0ToInspecInfo(testCase);
                Console.WriteLine($"  输入: {testCase ?? "null"}");
                Console.WriteLine($"  输出: {result}");
                Console.WriteLine($"  验证: {(result.StartsWith("CAM=C1;TS=;JD=;;Items=;") ? "✅" : "❌")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  输入: {testCase ?? "null"}");
                Console.WriteLine($"  异常: {ex.Message}");
                Console.WriteLine($"  验证: ❌");
            }
            Console.WriteLine();
        }
    }
    
    // 复制转换函数用于测试
    private string ConvertIn0ToInspecInfo(string in0Value)
    {
        if (string.IsNullOrEmpty(in0Value))
        {
            return "CAM=C1;TS=;JD=;;Items=;";
        }
        
        StringBuilder inspecInfoBuilder = new StringBuilder();
        inspecInfoBuilder.Append("CAM=C1;TS=;JD=;;Items=;");
        
        try
        {
            if (in0Value.Contains("="))
            {
                ProcessKeyValueFormat(in0Value, inspecInfoBuilder);
            }
            else if (in0Value.Contains(","))
            {
                ProcessCommaDelimitedFormat(in0Value, inspecInfoBuilder);
            }
            else if (in0Value.Contains(";"))
            {
                ProcessSemicolonDelimitedFormat(in0Value, inspecInfoBuilder);
            }
            else if (IsNumericValue(in0Value))
            {
                inspecInfoBuilder.Append($"检测项目1={in0Value};");
            }
            else
            {
                inspecInfoBuilder.Append($"检测项目1={in0Value};");
            }
        }
        catch (Exception)
        {
            return "CAM=C1;TS=;JD=;;Items=;检测项目1=0.0;";
        }
        
        return inspecInfoBuilder.ToString();
    }
    
    private void ProcessKeyValueFormat(string input, StringBuilder builder)
    {
        string[] pairs = input.Split(';');
        foreach (string pair in pairs)
        {
            if (!string.IsNullOrWhiteSpace(pair) && pair.Contains("="))
            {
                string cleanPair = pair.Trim();
                string[] keyValue = cleanPair.Split('=');
                if (keyValue.Length == 2)
                {
                    string key = keyValue[0].Trim();
                    string value = keyValue[1].Trim();
                    builder.Append($"{key}={value};");
                }
            }
        }
    }
    
    private void ProcessCommaDelimitedFormat(string input, StringBuilder builder)
    {
        string[] values = input.Split(',');
        for (int i = 0; i < values.Length; i++)
        {
            string value = values[i].Trim();
            if (!string.IsNullOrWhiteSpace(value))
            {
                builder.Append($"检测项目{i + 1}={value};");
            }
        }
    }
    
    private void ProcessSemicolonDelimitedFormat(string input, StringBuilder builder)
    {
        string[] values = input.Split(';');
        int itemIndex = 1;
        foreach (string value in values)
        {
            string cleanValue = value.Trim();
            if (!string.IsNullOrWhiteSpace(cleanValue))
            {
                builder.Append($"检测项目{itemIndex}={cleanValue};");
                itemIndex++;
            }
        }
    }
    
    private bool IsNumericValue(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return false;
            
        double result;
        return double.TryParse(value, out result);
    }
}
