using System;
using System.Text;
using System.Windows.Forms;
using Script.Methods;
/************************************
Shell Module default code: using .NET Framwwork 4.6.1
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;  

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;
       
    }

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
        {
            // 角度正数化处理：将-180~+180范围转换为0~360
            if (!string.IsNullOrEmpty(dangle))
            {
                string[] angleParts = dangle.Split(';');
                StringBuilder positiveAngles = new StringBuilder();
                
                foreach (string angleStr in angleParts)
                {
                    double angle;
                    if (double.TryParse(angleStr, out angle))
                    {
                        // 负数角度转换为正数（如-90° → 270°）
                        if (angle < 0)
                        {
                            angle += 360;
                        }
                        // 保留三位小数，与原始数据格式一致
                        positiveAngles.Append(angle.ToString("F3") + ";");
                    }
                    else
                    {
                        // 转换失败时保留原始值
                        positiveAngles.Append(angleStr + ";");
                    }
                }
                
                // 移除末尾多余的分号
                if (positiveAngles.Length > 0)
                {
                    positiveAngles.Length--;
                }
                
                // 将处理后的结果写回dangle变量
                outdangle = positiveAngles.ToString();
            }
            
            return true;
        }
}
                            