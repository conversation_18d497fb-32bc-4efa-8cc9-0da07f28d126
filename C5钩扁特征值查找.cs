using System;
using System.Text;
using System.Windows.Forms;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using Script.Methods;
/************************************
Shell Module default code: using .NET Framwwork 4.6.1
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;

    // 特征值数据库，存储历史特征值
    private List<FeatureData> featureDatabase;

    // 特征值匹配阈值
    private double angleThreshold = 5.0;  // 角度阈值
    private double distanceThreshold = 10.0;  // 距离阈值

    // 图像存储根目录
    private string imageStorageRoot = @"D:\HookImages";

    /// <summary>
    /// 特征值数据结构
    /// </summary>
    public class FeatureData
    {
        public List<double> Angles { get; set; }
        public List<double> Distances { get; set; }
        public string ImagePath { get; set; }
        public DateTime CreateTime { get; set; }

        public FeatureData()
        {
            Angles = new List<double>();
            Distances = new List<double>();
            CreateTime = DateTime.Now;
        }
    }

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;
        featureDatabase = new List<FeatureData>();

        // 确保图像存储目录存在
        if (!Directory.Exists(imageStorageRoot))
        {
            Directory.CreateDirectory(imageStorageRoot);
        }
    }

    /// <summary>
    /// 解析字符串特征值
    /// </summary>
    /// <param name="dataString">特征值字符串</param>
    /// <returns>特征值列表</returns>
    private List<double> ParseFeatureString(string dataString)
    {
        List<double> values = new List<double>();
        if (!string.IsNullOrEmpty(dataString))
        {
            string[] parts = dataString.Split(';');
            foreach (string part in parts)
            {
                if (double.TryParse(part, out double value))
                {
                    values.Add(value);
                }
            }
        }
        return values;
    }

    /// <summary>
    /// 计算两个特征值序列的相似度
    /// </summary>
    /// <param name="angles1">角度序列1</param>
    /// <param name="distances1">距离序列1</param>
    /// <param name="angles2">角度序列2</param>
    /// <param name="distances2">距离序列2</param>
    /// <returns>相似度分数（0-1，1为完全匹配）</returns>
    private double CalculateSimilarity(List<double> angles1, List<double> distances1,
                                     List<double> angles2, List<double> distances2)
    {
        if (angles1.Count != angles2.Count || distances1.Count != distances2.Count)
            return 0.0;

        double angleScore = 0.0;
        double distanceScore = 0.0;

        // 计算角度相似度
        for (int i = 0; i < angles1.Count; i++)
        {
            double angleDiff = Math.Abs(angles1[i] - angles2[i]);
            if (angleDiff <= angleThreshold)
            {
                angleScore += (angleThreshold - angleDiff) / angleThreshold;
            }
        }
        angleScore /= angles1.Count;

        // 计算距离相似度
        for (int i = 0; i < distances1.Count; i++)
        {
            double distanceDiff = Math.Abs(distances1[i] - distances2[i]);
            if (distanceDiff <= distanceThreshold)
            {
                distanceScore += (distanceThreshold - distanceDiff) / distanceThreshold;
            }
        }
        distanceScore /= distances1.Count;

        // 综合相似度（角度和距离各占50%权重）
        return (angleScore + distanceScore) / 2.0;
    }

    /// <summary>
    /// 保存图像到本地
    /// </summary>
    /// <param name="image">VM图像对象</param>
    /// <param name="angles">角度特征值</param>
    /// <param name="distances">距离特征值</param>
    /// <returns>保存的文件路径</returns>
    private string SaveImageToLocal(object image, List<double> angles, List<double> distances)
    {
        try
        {
            // 创建基于日期时间的文件夹
            DateTime now = DateTime.Now;
            string dateFolder = now.ToString("yyyy-MM-dd");
            string timeStamp = now.ToString("HHmmss_fff");

            string folderPath = Path.Combine(imageStorageRoot, dateFolder);
            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }

            // 生成文件名
            string fileName = $"Hook_{timeStamp}.bmp";
            string filePath = Path.Combine(folderPath, fileName);

            // 保存图像（这里需要根据VM的IMAGE类型调用相应的保存方法）
            // 假设VM的IMAGE对象有SaveImage方法
            if (image != null)
            {
                // 这里需要根据实际的VM IMAGE API进行调用
                // 示例：((VisionMaster.Image)image).SaveImage(filePath);
                // 由于无法确定具体的VM API，这里使用反射调用
                var imageType = image.GetType();
                var saveMethod = imageType.GetMethod("SaveImage");
                if (saveMethod != null)
                {
                    saveMethod.Invoke(image, new object[] { filePath });
                }
            }

            return filePath;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"保存图像失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return null;
        }
    }

    /// <summary>
    /// 查找匹配的特征值
    /// </summary>
    /// <param name="currentAngles">当前角度特征值</param>
    /// <param name="currentDistances">当前距离特征值</param>
    /// <param name="similarityThreshold">相似度阈值</param>
    /// <returns>匹配的特征值数据列表</returns>
    private List<FeatureData> FindMatchingFeatures(List<double> currentAngles, List<double> currentDistances,
                                                   double similarityThreshold = 0.8)
    {
        List<FeatureData> matches = new List<FeatureData>();

        foreach (var feature in featureDatabase)
        {
            double similarity = CalculateSimilarity(currentAngles, currentDistances,
                                                   feature.Angles, feature.Distances);
            if (similarity >= similarityThreshold)
            {
                matches.Add(feature);
            }
        }

        // 按相似度排序
        matches = matches.OrderByDescending(f => CalculateSimilarity(currentAngles, currentDistances,
                                                                    f.Angles, f.Distances)).ToList();

        return matches;
    }

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
        try
        {
            // 获取输入参数
            string dangleStr = GetInput("dangle") as string;
            string disStr = GetInput("dis") as string;
            object picImage = GetInput("pic");

            if (string.IsNullOrEmpty(dangleStr) || string.IsNullOrEmpty(disStr) || picImage == null)
            {
                MessageBox.Show("输入参数不完整，请检查dangle、dis和pic参数", "警告",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            // 解析特征值
            List<double> currentAngles = ParseFeatureString(dangleStr);
            List<double> currentDistances = ParseFeatureString(disStr);

            if (currentAngles.Count == 0 || currentDistances.Count == 0)
            {
                MessageBox.Show("特征值解析失败，请检查输入格式", "错误",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            // 查找匹配的特征值
            List<FeatureData> matches = FindMatchingFeatures(currentAngles, currentDistances);

            // 保存当前图像
            string savedImagePath = SaveImageToLocal(picImage, currentAngles, currentDistances);

            if (!string.IsNullOrEmpty(savedImagePath))
            {
                // 创建新的特征值数据
                FeatureData newFeature = new FeatureData
                {
                    Angles = currentAngles,
                    Distances = currentDistances,
                    ImagePath = savedImagePath
                };

                // 添加到特征值数据库
                featureDatabase.Add(newFeature);

                // 输出匹配结果
                if (matches.Count > 0)
                {
                    string matchInfo = $"找到 {matches.Count} 个匹配的特征值:\n";
                    for (int i = 0; i < Math.Min(matches.Count, 5); i++)
                    {
                        double similarity = CalculateSimilarity(currentAngles, currentDistances,
                                                               matches[i].Angles, matches[i].Distances);
                        matchInfo += $"匹配 {i + 1}: 相似度 {similarity:F3}, 图像: {matches[i].ImagePath}\n";
                    }

                    MessageBox.Show(matchInfo, "匹配结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("未找到匹配的特征值", "匹配结果",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                MessageBox.Show($"图像已保存到: {savedImagePath}", "保存成功",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

            processCount++;
            return true;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"处理过程中发生错误: {ex.Message}", "错误",
                          MessageBoxButtons.OK, MessageBoxIcon.Error);
            return false;
        }
    }
}
                            