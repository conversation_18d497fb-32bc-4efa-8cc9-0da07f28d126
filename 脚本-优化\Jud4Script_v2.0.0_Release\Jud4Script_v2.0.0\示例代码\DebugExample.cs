using System;
using Jud4Script;

/// <summary>
/// Jud4Script 调试功能示例
/// </summary>
class DebugExample
{
    static void Main()
    {
        Console.WriteLine("=== Jud4Script 调试功能示例 ===");
        
        // 示例1：基本调试功能
        Console.WriteLine("示例1 - 基本调试功能:");
        
        // 启用调试模式
        JudgmentEngine.SetDebugEnabled(true);
        Console.WriteLine("✓ 调试模式已启用");
        
        // 执行一个正常的判定
        string result1 = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7");
        Console.WriteLine($"判定结果: {result1}");
        
        // 获取调试信息
        string debugInfo1 = JudgmentEngine.GetDebugInfo();
        Console.WriteLine("调试信息:");
        Console.WriteLine(debugInfo1);
        Console.WriteLine(new string('=', 50));
        
        // 示例2：异常情况调试
        Console.WriteLine("示例2 - 异常情况调试:");
        
        // 清空之前的调试信息
        JudgmentEngine.ClearDebugInfo();
        
        // 执行一个会产生异常的判定
        string result2 = JudgmentEngine.Evaluate("INVALID-MODEL", "Items=;槽深=0.7");
        Console.WriteLine($"异常判定结果: {result2}");
        
        // 获取异常调试信息
        string debugInfo2 = JudgmentEngine.GetDebugInfo();
        Console.WriteLine("异常调试信息:");
        Console.WriteLine(debugInfo2);
        Console.WriteLine(new string('=', 50));
        
        // 示例3：格式错误调试
        Console.WriteLine("示例3 - 格式错误调试:");
        
        JudgmentEngine.ClearDebugInfo();
        
        // 单值多测量值错误
        string result3 = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7,0.72,0.75");
        Console.WriteLine($"格式错误结果: {result3}");
        
        string debugInfo3 = JudgmentEngine.GetDebugInfo();
        Console.WriteLine("格式错误调试信息:");
        Console.WriteLine(debugInfo3);
        Console.WriteLine(new string('=', 50));
        
        // 示例4：多值处理调试
        Console.WriteLine("示例4 - 多值处理调试:");
        
        JudgmentEngine.ClearDebugInfo();
        
        // 多值判定
        string result4 = JudgmentEngine.Evaluate("0523A-F", "Items=;槽宽*3=0.32,0.31,0.35");
        Console.WriteLine($"多值判定结果: {result4}");
        
        string debugInfo4 = JudgmentEngine.GetDebugInfo();
        Console.WriteLine("多值处理调试信息:");
        Console.WriteLine(debugInfo4);
        Console.WriteLine(new string('=', 50));
        
        // 示例5：复杂场景调试
        Console.WriteLine("示例5 - 复杂场景调试:");
        
        JudgmentEngine.ClearDebugInfo();
        
        // 复杂的多检测项判定
        string complexInput = "CAM=C5;TS=;PD=OK;POF=;Items=;" +
                             "槽深=0.7;" +
                             "槽宽*2=0.4,0.42;" +
                             "槽槽夹角*3=-100,0,100";
        
        string result5 = JudgmentEngine.Evaluate("0523A-F", complexInput);
        Console.WriteLine($"复杂场景结果: {result5}");
        
        string debugInfo5 = JudgmentEngine.GetDebugInfo();
        Console.WriteLine("复杂场景调试信息:");
        Console.WriteLine(debugInfo5);
        Console.WriteLine(new string('=', 50));
        
        // 示例6：调试信息分析
        Console.WriteLine("示例6 - 调试信息分析:");
        
        JudgmentEngine.ClearDebugInfo();
        string analysisResult = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.55"); // 轻微不良
        string analysisDebug = JudgmentEngine.GetDebugInfo();
        
        Console.WriteLine($"分析案例结果: {analysisResult}");
        Console.WriteLine("关键调试信息提取:");
        
        // 分析调试信息
        string[] debugLines = analysisDebug.Split('\n');
        foreach (string line in debugLines)
        {
            if (line.Contains("[ERROR]"))
            {
                Console.WriteLine($"  错误: {line.Trim()}");
            }
            else if (line.Contains("判定结果"))
            {
                Console.WriteLine($"  判定: {line.Trim()}");
            }
            else if (line.Contains("步骤"))
            {
                Console.WriteLine($"  步骤: {line.Trim()}");
            }
        }
        Console.WriteLine(new string('=', 50));
        
        // 示例7：性能影响测试
        Console.WriteLine("示例7 - 性能影响测试:");
        
        int testCount = 1000;
        
        // 禁用调试模式测试
        JudgmentEngine.SetDebugEnabled(false);
        DateTime start1 = DateTime.Now;
        for (int i = 0; i < testCount; i++)
        {
            JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7");
        }
        DateTime end1 = DateTime.Now;
        double time1 = (end1 - start1).TotalMilliseconds;
        
        // 启用调试模式测试
        JudgmentEngine.SetDebugEnabled(true);
        DateTime start2 = DateTime.Now;
        for (int i = 0; i < testCount; i++)
        {
            JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7");
            JudgmentEngine.ClearDebugInfo(); // 避免内存累积
        }
        DateTime end2 = DateTime.Now;
        double time2 = (end2 - start2).TotalMilliseconds;
        
        Console.WriteLine($"执行{testCount}次判定的性能对比:");
        Console.WriteLine($"  禁用调试: {time1:F2} ms ({time1/testCount:F3} ms/次)");
        Console.WriteLine($"  启用调试: {time2:F2} ms ({time2/testCount:F3} ms/次)");
        Console.WriteLine($"  性能影响: {((time2-time1)/time1*100):F1}%");
        Console.WriteLine();
        
        // 示例8：调试最佳实践
        Console.WriteLine("示例8 - 调试最佳实践:");
        
        Console.WriteLine("调试模式使用建议:");
        Console.WriteLine("1. 开发阶段启用调试模式进行问题定位");
        Console.WriteLine("2. 生产环境建议禁用调试模式以提高性能");
        Console.WriteLine("3. 出现异常时临时启用调试模式获取详细信息");
        Console.WriteLine("4. 定期清空调试信息避免内存累积");
        Console.WriteLine();
        
        // 演示条件调试
        bool isDebugMode = true; // 可以通过配置文件或命令行参数控制
        
        if (isDebugMode)
        {
            JudgmentEngine.SetDebugEnabled(true);
            Console.WriteLine("✓ 开发模式：调试已启用");
        }
        else
        {
            JudgmentEngine.SetDebugEnabled(false);
            Console.WriteLine("✓ 生产模式：调试已禁用");
        }
        
        // 执行判定
        string finalResult = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7");
        Console.WriteLine($"最终判定结果: {finalResult}");
        
        if (isDebugMode)
        {
            string finalDebug = JudgmentEngine.GetDebugInfo();
            Console.WriteLine("最终调试信息:");
            Console.WriteLine(finalDebug);
        }
        
        Console.WriteLine();
        Console.WriteLine("=== 调试功能示例完成 ===");
        Console.WriteLine("调试功能总结:");
        Console.WriteLine("- SetDebugEnabled(bool): 启用/禁用调试模式");
        Console.WriteLine("- GetDebugInfo(): 获取详细调试信息");
        Console.WriteLine("- ClearDebugInfo(): 清空调试信息缓存");
        Console.WriteLine("- 调试信息包含时间戳、步骤跟踪、错误详情");
        Console.WriteLine("- 生产环境建议禁用调试以提高性能");
        
        Console.WriteLine();
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}
