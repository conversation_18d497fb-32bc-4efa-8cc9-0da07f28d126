# Jud4Script 动态相机编号脚本说明

## 📋 概述

这是一个智能的Jud4Script集成脚本，能够根据输入数值动态识别相机编号（C1, C2, C2A等），自动识别分隔符，处理每个项目后重新组合成正确的格式。

## 🎯 核心功能

```csharp
public bool Process()
{
    // 动态识别相机编号并转换in0为inspecInfo格式
    string inspecInfo = ConvertIn0ToInspecInfo(in0);
    
    // 调用判定引擎
    string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);
    
    // 输出结果
    out0 = result;
    
    return true;
}
```

## 🔧 动态识别逻辑

### 相机编号识别优先级
1. **C2A** → CAM2A (优先识别复合编号)
2. **C1** → CAM1
3. **C2** → CAM2  
4. **C3** → CAM3
5. **C4** → CAM4
6. **C5** → CAM5
7. **默认** → CAM1 (无相机编号时)

### 处理流程
1. **识别相机**: 从输入字符串中提取相机编号
2. **分割数据**: 根据分隔符(`;`或`,`)分割字符串
3. **处理项目**: 移除相机编号，V→=替换
4. **重新组合**: 组合成标准格式输出

## 📊 转换示例

### 示例1：C1相机
```
输入: "C1外圆碰伤V1.250;C1外圆纹路V0.500;C1槽内多铜V0.000"

处理过程:
1. 识别相机: C1 → CAM1
2. 分割: ["C1外圆碰伤V1.250", "C1外圆纹路V0.500", "C1槽内多铜V0.000"]
3. 处理: ["外圆碰伤=1.250", "外圆纹路=0.500", "槽内多铜=0.000"]
4. 组合: "外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"

输出: "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"
```

### 示例2：C2A相机
```
输入: "C2A外圆碰伤V1.250,C2A外圆纹路V0.500"

处理过程:
1. 识别相机: C2A → CAM2A
2. 分割: ["C2A外圆碰伤V1.250", "C2A外圆纹路V0.500"]
3. 处理: ["外圆碰伤=1.250", "外圆纹路=0.500"]
4. 组合: "外圆碰伤=1.250;外圆纹路=0.500;"

输出: "CAM=CAM2A;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;"
```

### 示例3：C5相机
```
输入: "C5槽深V0.750;C5槽宽V0.450;C5槽槽夹角V175.0"

处理过程:
1. 识别相机: C5 → CAM5
2. 分割: ["C5槽深V0.750", "C5槽宽V0.450", "C5槽槽夹角V175.0"]
3. 处理: ["槽深=0.750", "槽宽=0.450", "槽槽夹角=175.0"]
4. 组合: "槽深=0.750;槽宽=0.450;槽槽夹角=175.0;"

输出: "CAM=CAM5;TS=;JD=;;Items=;槽深=0.750;槽宽=0.450;槽槽夹角=175.0;"
```

### 示例4：无相机编号（默认）
```
输入: "外圆碰伤V1.250;外圆纹路V0.500"

处理过程:
1. 识别相机: 无 → CAM1 (默认)
2. 分割: ["外圆碰伤V1.250", "外圆纹路V0.500"]
3. 处理: ["外圆碰伤=1.250", "外圆纹路=0.500"]
4. 组合: "外圆碰伤=1.250;外圆纹路=0.500;"

输出: "CAM=CAM1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;"
```

### 示例5：混合格式
```
输入: "C2外圆碰伤V1.250;外圆纹路=0.500;C2槽内多铜V0.000"

处理过程:
1. 识别相机: C2 → CAM2
2. 分割: ["C2外圆碰伤V1.250", "外圆纹路=0.500", "C2槽内多铜V0.000"]
3. 处理: ["外圆碰伤=1.250", "外圆纹路=0.500", "槽内多铜=0.000"]
4. 组合: "外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"

输出: "CAM=CAM2;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"
```

## 🚀 使用方法

### VisionMaster中调用
```csharp
// 设置输入参数 - 支持多种相机编号格式
modelNo = "0523A-F";

// C1相机格式
in0 = "C1外圆碰伤V1.250;C1外圆纹路V0.500";

// C2A相机格式
in0 = "C2A外圆碰伤V1.250,C2A外圆纹路V0.500";

// C5相机格式
in0 = "C5槽深V0.750;C5槽宽V0.450";

// 无相机编号（默认C1）
in0 = "外圆碰伤V1.250;外圆纹路V0.500";

// 调用Process()函数
// 自动识别相机编号和格式，转换并调用JudgmentEngine.Evaluate(modelNo, inspecInfo)

// 获取结果
string result = out0;
```

## 📈 核心算法

### 相机编号提取函数
```csharp
private string ExtractCameraId(string input)
{
    if (string.IsNullOrEmpty(input))
    {
        return "CAM1";
    }

    // 查找C1, C2, C2A等模式（注意C2A优先于C2）
    if (input.Contains("C1"))
    {
        return "CAM1";
    }
    else if (input.Contains("C2A"))
    {
        return "CAM2A";
    }
    else if (input.Contains("C2"))
    {
        return "CAM2";
    }
    else if (input.Contains("C3"))
    {
        return "CAM3";
    }
    else if (input.Contains("C4"))
    {
        return "CAM4";
    }
    else if (input.Contains("C5"))
    {
        return "CAM5";
    }

    // 默认返回CAM1
    return "CAM1";
}
```

### 项目处理函数
```csharp
private string ProcessItem(string item)
{
    // 移除相机编号（C1, C2, C2A等）
    string processed = item;
    processed = processed.Replace("C1", "");
    processed = processed.Replace("C2A", "");  // 先处理C2A
    processed = processed.Replace("C2", "");   // 再处理C2
    processed = processed.Replace("C3", "");
    processed = processed.Replace("C4", "");
    processed = processed.Replace("C5", "");

    // 将V替换为=
    processed = processed.Replace("V", "=");

    return processed;
}
```

## ✅ 特点优势

1. **动态识别**: 根据输入自动识别相机编号
2. **多相机支持**: 支持C1, C2, C2A, C3, C4, C5等
3. **智能分割**: 自动识别分号或逗号分隔符
4. **格式清理**: 自动移除相机编号，保留检测项目名称
5. **容错处理**: 无相机编号时默认使用CAM1

## 🔧 支持的相机编号

| 输入格式 | 识别结果 | 示例 |
|---------|---------|------|
| C1xxx | CAM1 | `"C1外圆碰伤V1.0"` |
| C2xxx | CAM2 | `"C2外圆碰伤V1.0"` |
| C2Axxx | CAM2A | `"C2A外圆碰伤V1.0"` |
| C3xxx | CAM3 | `"C3外圆碰伤V1.0"` |
| C4xxx | CAM4 | `"C4外圆碰伤V1.0"` |
| C5xxx | CAM5 | `"C5外圆碰伤V1.0"` |
| 无编号 | CAM1 | `"外圆碰伤V1.0"` |

## ⚠️ 注意事项

1. **识别优先级**: C2A优先于C2识别，避免误判
2. **相机编号移除**: 处理后的项目名称不包含相机编号
3. **默认相机**: 无相机编号时默认使用CAM1
4. **V替换**: 所有V都会被替换为=
5. **分隔符优先级**: 分号优先于逗号

## 🎯 适用场景

- ✅ **多相机系统**: 支持不同相机的数据处理
- ✅ **动态适配**: 根据输入自动调整相机编号
- ✅ **格式统一**: 自动转换为标准格式
- ✅ **向前兼容**: 支持无相机编号的旧格式

---

**总结**: 这是一个智能的动态相机编号识别脚本，能够根据输入数值自动识别相机编号，确保JudgmentEngine.Evaluate()接收到正确的CAM参数。
