Jud4Script.dll 版本信息
========================

版本号: 1.0.1
发布日期: 2025-07-11
构建时间: 2025-07-11 01:30

兼容性:
- VisionMaster 4.3.0+
- .NET Framework 4.8+
- Windows 7/10/11

依赖库:
- Newtonsoft.Json ********

文件清单:
- Jud4Script.dll (主要动态库)
- Newtonsoft.Json.dll (JSON处理依赖)
- 配置文件示例 (3个产品型号)
- 完整文档 (4个文档文件)
- 安装脚本 (批处理和PowerShell)
- 测试程序 (安装验证)

更新内容:
- 更新配置文件格式（支持元数据字段）
- 更新输入格式（支持新的Items=格式）
- 支持多值检测项目（*n格式）
- 完善的异常处理机制
- 配置文件缓存优化
- 线程安全设计
- 向后兼容旧格式

安装要求:
- 管理员权限（用于创建配置目录）
- 配置文件必须使用UTF-8编码
- DLL文件必须与依赖库在同一目录

技术特点:
- 高性能配置文件缓存
- 线程安全的并发处理
- 完善的输入验证
- 详细的错误信息
- 灵活的配置约束验证
