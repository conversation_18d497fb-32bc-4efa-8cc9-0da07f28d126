# Jud4Script 通用脚本使用说明

## 📋 概述

这是一个通用的Jud4Script集成脚本，能够动态读取配置文件来获取检测项目名称和相机编号，无需硬编码，适用于不同的产品型号。

## 🎯 核心特性

### ✅ 动态配置读取
- 自动从 `D:\LvConfig\产品型号\{modelNo}.json` 读取配置
- 动态获取检测项目名称列表
- 自动识别相机编号(C1, C5等)

### ✅ 多格式输入支持
- 键值对格式：`"外圆面伤=180.0;外圆纹路=0.0"`
- 逗号分隔：`"180.0,0.0,0.0"`
- 分号分隔：`"180.0;0.0;0.0"`
- 单个数值：`"180.0"`

### ✅ 智能容错处理
- 配置文件不存在时使用默认项目名称
- 解析失败时自动降级处理
- 无异常抛出，保证脚本稳定运行

## 🔧 工作原理

### 1. 配置文件读取
脚本会读取以下配置文件结构：
```json
{
  "检测项目列表": [
    {
      "检测项目": "外圆面伤",
      "分属相机": "相机1"
    },
    {
      "检测项目": "外圆纹路", 
      "分属相机": "相机1"
    },
    {
      "检测项目": "槽深",
      "分属相机": "相机5"
    }
  ]
}
```

### 2. 动态项目映射
- 从配置文件提取所有"检测项目"字段值
- 按顺序建立数组格式的映射关系
- 支持任意数量的检测项目

### 3. 相机编号识别
- 读取第一个检测项目的"分属相机"字段
- 自动转换为相机编号：
  - "相机1" → "C1"
  - "相机5" → "C5"
  - "相机2" → "C2"
  - 等等...

## 📊 转换示例

### 示例1：0523A-F产品
**配置文件内容**：
```json
{
  "检测项目列表": [
    {"检测项目": "外圆面伤", "分属相机": "相机1"},
    {"检测项目": "外圆纹路", "分属相机": "相机1"},
    {"检测项目": "槽内多铜", "分属相机": "相机1A"}
  ]
}
```

**转换过程**：
```csharp
// 输入
modelNo = "0523A-F"
in0 = "180.0,0.0,50.0"

// 动态读取得到
检测项目列表 = ["外圆面伤", "外圆纹路", "槽内多铜"]
相机编号 = "C1"

// 转换结果
inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆面伤=180.0;外圆纹路=0.0;槽内多铜=50.0;"
```

### 示例2：其他产品型号
**配置文件内容**：
```json
{
  "检测项目列表": [
    {"检测项目": "槽深", "分属相机": "相机5"},
    {"检测项目": "槽宽", "分属相机": "相机5"},
    {"检测项目": "槽槽夹角", "分属相机": "相机5"}
  ]
}
```

**转换过程**：
```csharp
// 输入
modelNo = "其他型号"
in0 = "0.75,0.45,175.0"

// 动态读取得到
检测项目列表 = ["槽深", "槽宽", "槽槽夹角"]
相机编号 = "C5"

// 转换结果
inspecInfo = "CAM=C5;TS=;JD=;;Items=;槽深=0.75;槽宽=0.45;槽槽夹角=175.0;"
```

## 🚀 使用方法

### 1. 准备配置文件
在 `D:\LvConfig\产品型号\` 目录下创建对应的JSON配置文件：
- 文件名：`{modelNo}.json`
- 格式：标准的Jud4Script配置格式
- 编码：UTF-8

### 2. VisionMaster中调用
```csharp
// 设置输入参数
modelNo = "您的产品型号";  // 如"0523A-F"
in0 = "您的检测数据";      // 如"180.0,0.0,50.0"

// 调用Process()函数
// 脚本会自动：
// 1. 读取配置文件获取检测项目名称
// 2. 转换in0为inspecInfo格式
// 3. 调用JudgmentEngine.Evaluate(modelNo, inspecInfo)
// 4. 输出结果到out0

// 获取结果
string result = out0;
```

### 3. 核心代码流程
```csharp
public bool Process()
{
    // 将in0参数转换为inspecInfo格式（动态读取配置）
    string inspecInfo = ConvertIn0ToInspecInfo(in0);
    
    // 调用判定引擎
    string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);
    
    // 输出结果
    out0 = result;
    
    return true;
}
```

## 🔍 容错机制

### 配置文件不存在
```csharp
// 使用默认检测项目名称
默认项目 = ["检测项目1", "检测项目2", "检测项目3"]
默认相机 = "C1"
```

### JSON解析失败
```csharp
// 降级到默认配置
// 脚本继续运行，不会中断
```

### 输入数据异常
```csharp
// 空值处理
if (string.IsNullOrEmpty(in0))
    return "CAM=C1;TS=;JD=;;Items=;";
```

## ⚙️ 自定义配置

### 修改默认检测项目
在 `GetDefectItemNames()` 函数中修改默认返回值：
```csharp
// 配置文件不存在时的默认项目
return new string[] { "您的默认项目1", "您的默认项目2", "您的默认项目3" };
```

### 修改相机编号映射
在 `GetCameraId()` 函数中添加更多映射规则：
```csharp
if (cameraName.Contains("6"))
    return "C6";
else if (cameraName.Contains("7"))
    return "C7";
// 添加更多相机编号...
```

## 📈 优势特点

1. **通用性强**：适用于任何产品型号，无需修改代码
2. **配置驱动**：通过配置文件控制检测项目，灵活性高
3. **自动适配**：自动识别相机编号和检测项目数量
4. **稳定可靠**：完善的容错机制，不会因配置问题导致脚本失败
5. **易于维护**：新增产品型号只需添加配置文件

## ⚠️ 注意事项

1. **配置文件路径**：确保路径 `D:\LvConfig\产品型号\` 存在
2. **文件命名**：配置文件名必须与modelNo完全一致
3. **编码格式**：配置文件必须使用UTF-8编码
4. **JSON格式**：确保配置文件是有效的JSON格式
5. **权限要求**：确保脚本有读取配置文件的权限

---

**重要提醒**：
- 这是一个通用脚本，适用于所有使用Jud4Script的产品型号
- 新增产品型号时，只需添加对应的配置文件即可
- 无需修改脚本代码，完全通过配置文件驱动
