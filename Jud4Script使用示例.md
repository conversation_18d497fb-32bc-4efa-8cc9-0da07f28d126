# Jud4Script 使用示例

## 📋 概述

本文档提供了在VisionMaster中使用Jud4Script进行产品质量判定的完整示例。

## 🔧 配置步骤

### 1. 准备配置文件

在 `D:\LvConfig\产品型号\` 目录下创建对应的JSON配置文件：

**文件路径**: `D:\LvConfig\产品型号\0523A-F.json`

```json
{
  "配置版本": "1.0",
  "型号名称": "0523A-F",
  "检测项目列表": [
    {
      "检测项目": "外圆面伤",
      "分属相机": "相机1",
      "严重分类": "4",
      "轻微分类": "3",
      "良品分类": "1",
      "轻微上限": 200.0,
      "良品上限": 180.0,
      "良品下限": 0.0,
      "轻微下限": 0.0
    },
    {
      "检测项目": "外圆纹路",
      "分属相机": "相机1",
      "严重分类": "4",
      "轻微分类": "3",
      "良品分类": "1",
      "轻微上限": 50.0,
      "良品上限": 30.0,
      "良品下限": 0.0,
      "轻微下限": 0.0
    },
    {
      "检测项目": "槽内多铜",
      "分属相机": "相机1",
      "严重分类": "4",
      "轻微分类": "3",
      "良品分类": "1",
      "轻微上限": 10.0,
      "良品上限": 5.0,
      "良品下限": 0.0,
      "轻微下限": 0.0
    }
  ]
}
```

### 2. VisionMaster脚本配置

在VisionMaster中设置输入输出变量：

**输入变量**:
- `modelNo` (string): "0523A-F"
- `in0` (string): 检测结果数据

**输出变量**:
- `out0` (string): 判定结果

## 📊 使用场景示例

### 场景1：单个检测项目

**输入数据**:
```
modelNo = "0523A-F"
in0 = "150.5"
```

**转换过程**:
```
inspecInfo = "CAM=C1;TS=;JD=;;Items=;检测项目1=150.5;"
```

**判定结果**:
```
out0 = "1"  // 良品（假设150.5在良品范围内）
```

### 场景2：多个检测项目（键值对格式）

**输入数据**:
```
modelNo = "0523A-F"
in0 = "外圆面伤=150.0;外圆纹路=25.0;槽内多铜=3.0"
```

**转换过程**:
```
inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆面伤=150.0;外圆纹路=25.0;槽内多铜=3.0;"
```

**判定逻辑**:
- 外圆面伤=150.0 ≤ 180.0 (良品上限) → 良品
- 外圆纹路=25.0 ≤ 30.0 (良品上限) → 良品  
- 槽内多铜=3.0 ≤ 5.0 (良品上限) → 良品

**判定结果**:
```
out0 = "1"  // 所有项目都是良品
```

### 场景3：包含轻微缺陷

**输入数据**:
```
modelNo = "0523A-F"
in0 = "外圆面伤=190.0;外圆纹路=20.0;槽内多铜=2.0"
```

**判定逻辑**:
- 外圆面伤=190.0 > 180.0 (良品上限) 但 ≤ 200.0 (轻微上限) → 轻微
- 外圆纹路=20.0 ≤ 30.0 (良品上限) → 良品
- 槽内多铜=2.0 ≤ 5.0 (良品上限) → 良品

**判定结果**:
```
out0 = "3"  // 轻微分类（有一个项目是轻微）
```

### 场景4：包含严重缺陷

**输入数据**:
```
modelNo = "0523A-F"
in0 = "外圆面伤=250.0;外圆纹路=15.0;槽内多铜=1.0"
```

**判定逻辑**:
- 外圆面伤=250.0 > 200.0 (轻微上限) → 严重
- 外圆纹路=15.0 ≤ 30.0 (良品上限) → 良品
- 槽内多铜=1.0 ≤ 5.0 (良品上限) → 良品

**判定结果**:
```
out0 = "4"  // 严重分类（有一个项目是严重）
```

### 场景5：逗号分隔格式

**输入数据**:
```
modelNo = "0523A-F"
in0 = "150.0,25.0,3.0"
```

**转换过程**:
```
inspecInfo = "CAM=C1;TS=;JD=;;Items=;检测项目1=150.0;检测项目2=25.0;检测项目3=3.0;"
```

**注意**: 这种格式需要确保配置文件中有对应的"检测项目1"、"检测项目2"等项目名称。

## 🔍 调试和验证

### 1. 启用日志记录

脚本会自动在 `D:\HODD\Jud4Script_Log.txt` 中记录详细信息：

```
2025-07-12 09:35:00.123 - 产品型号: 0523A-F, 检测信息: CAM=C1;TS=;JD=;;Items=;外圆面伤=150.0;外圆纹路=25.0;槽内多铜=3.0;, 判定结果: 1
```

### 2. 运行测试程序

编译并运行 `Jud4Script转换测试.cs` 来验证转换功能：

```bash
csc Jud4Script转换测试.cs
Jud4Script转换测试.exe
```

### 3. 验证配置文件

确保配置文件格式正确且路径存在：
- 文件路径: `D:\LvConfig\产品型号\{modelNo}.json`
- 编码格式: UTF-8
- JSON格式: 有效的JSON语法

## ⚠️ 常见问题和解决方案

### 问题1: 返回结果为"0"

**可能原因**:
- 配置文件不存在或路径错误
- 配置文件格式错误
- 检测项目名称不匹配
- Jud4Script.dll文件缺失

**解决方案**:
1. 检查配置文件路径和格式
2. 确保检测项目名称完全匹配
3. 检查DLL文件是否存在
4. 查看日志文件了解具体错误

### 问题2: 检测项目名称不匹配

**错误示例**:
```
配置文件中: "外圆面伤"
输入数据中: "外圆面伤1" 或 "外圆面伤 " (有空格)
```

**解决方案**:
确保输入数据中的检测项目名称与配置文件完全一致。

### 问题3: 数值格式错误

**错误示例**:
```
in0 = "外圆面伤=abc;外圆纹路=25.0"
```

**解决方案**:
确保所有数值都是有效的数字格式。

## 📈 性能优化建议

1. **配置文件缓存**: 避免每次都重新读取配置文件
2. **异常处理**: 完善的异常处理机制
3. **日志管理**: 定期清理日志文件
4. **参数验证**: 在转换前验证输入参数格式

## 🎯 最佳实践

1. **统一命名**: 使用一致的检测项目命名规范
2. **版本管理**: 为配置文件添加版本控制
3. **测试验证**: 在生产环境前充分测试
4. **文档维护**: 保持配置文档的更新

---

**重要提醒**: 
- 确保所有依赖文件都在正确位置
- 定期备份配置文件
- 监控日志文件以及时发现问题
