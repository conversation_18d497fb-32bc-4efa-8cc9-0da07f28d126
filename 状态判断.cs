using System;
using System.Text;
using System.Windows.Forms;
using System.IO;
using Script.Methods;
/************************************
Shell Module default code: using .NET Framwwork 4.6.1
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;  

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;
       
    }

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
        // 获取外部输入量（假设in0为判断值，in1为帧号）
        // 将外部输入转换为整数类型
        int inputFlag = jud; // 判断值（1或0）
        int frameNumber = fn; // 帧号
        string CamNum = cam; // 相机编号

        // 直接判断输入是否为1
        if (inputFlag == 0)
        {
            try
            {
                // 获取当前日期作为文件夹名
                string dateFolder = DateTime.Now.ToString("yyyyMMdd");
                string matErrPath = Path.Combine("D:\\", "Mat-ERR"); // Mat-ERR主目录路径
                string rootPath = Path.Combine(matErrPath, dateFolder); // 日期子目录路径

                // 创建Mat-ERR主目录（如果不存在）
                if (!Directory.Exists(matErrPath))
                {
                    Directory.CreateDirectory(matErrPath);
                }

                // 创建日期子目录（如果不存在）
                if (!Directory.Exists(rootPath))
                {
                    Directory.CreateDirectory(rootPath);
                }

                // 生成日志内容（包含时间、日期和帧号）
                string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                string logContent = string.Format("时间: {0}, 帧号: {1}, 相机: {2}", timestamp, frameNumber, CamNum);

                // 日志文件名（每天一个文件）
                string logFileName = string.Format("Log_{0}_{1}.txt", CamNum, DateTime.Now.ToString("yyyyMMdd"));
                string logFilePath = Path.Combine(rootPath, logFileName);

                // 追加日志内容（文件不存在时自动创建）
                File.AppendAllText(logFilePath, logContent + Environment.NewLine, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                // 异常处理（可根据需要扩展）
                Console.WriteLine("日志创建失败: " + ex.Message);
                return false;
            }
        }

        return true;
    }
}
                            