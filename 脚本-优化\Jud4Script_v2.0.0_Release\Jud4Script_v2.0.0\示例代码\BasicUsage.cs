using System;
using Jud4Script;

/// <summary>
/// Jud4Script 基本使用示例
/// </summary>
class BasicUsage
{
    static void Main()
    {
        Console.WriteLine("=== Jud4Script 基本使用示例 ===");
        
        // 示例1：单值判定
        Console.WriteLine("示例1 - 单值判定:");
        string result1 = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7");
        Console.WriteLine("输入: Items=;槽深=0.7");
        Console.WriteLine("输出: " + result1);
        Console.WriteLine("说明: 单个检测项的基本判定");
        Console.WriteLine();
        
        // 示例2：多值判定
        Console.WriteLine("示例2 - 多值判定:");
        string result2 = JudgmentEngine.Evaluate("0523A-F", "Items=;槽宽*3=0.32,0.31,0.35");
        Console.WriteLine("输入: Items=;槽宽*3=0.32,0.31,0.35");
        Console.WriteLine("输出: " + result2);
        Console.WriteLine("说明: 多个测量值，取最差判定结果");
        Console.WriteLine();
        
        // 示例3：多个检测项
        Console.WriteLine("示例3 - 多个检测项:");
        string result3 = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7;槽宽=0.4");
        Console.WriteLine("输入: Items=;槽深=0.7;槽宽=0.4");
        Console.WriteLine("输出: " + result3);
        Console.WriteLine("说明: 多个检测项的综合判定");
        Console.WriteLine();
        
        // 示例4：完整格式
        Console.WriteLine("示例4 - 完整格式:");
        string fullInput = "CAM=C5;TS=20250112;PD=OK;POF=;Items=;槽深=0.7;槽宽*2=0.4,0.42";
        string result4 = JudgmentEngine.Evaluate("0523A-F", fullInput);
        Console.WriteLine("输入: " + fullInput);
        Console.WriteLine("输出: " + result4);
        Console.WriteLine("说明: 包含所有字段的完整格式");
        Console.WriteLine();
        
        // 示例5：边界值测试
        Console.WriteLine("示例5 - 边界值测试:");
        
        // 良品边界
        string result5a = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.6");  // 良品下限
        Console.WriteLine("槽深=0.6 (良品下限): " + result5a);
        
        string result5b = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.8");  // 良品上限
        Console.WriteLine("槽深=0.8 (良品上限): " + result5b);
        
        // 轻微边界
        string result5c = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.55"); // 轻微范围
        Console.WriteLine("槽深=0.55 (轻微范围): " + result5c);
        
        // 严重不良
        string result5d = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.4");  // 严重不良
        Console.WriteLine("槽深=0.4 (严重不良): " + result5d);
        Console.WriteLine();
        
        // 示例6：结果解释
        Console.WriteLine("示例6 - 结果解释:");
        Console.WriteLine("返回值含义:");
        Console.WriteLine("  1 = 良品分类（出料口1）");
        Console.WriteLine("  5 = 轻微分类（出料口5）");
        Console.WriteLine("  9 = 严重分类（出料口9）");
        Console.WriteLine("  0 = 异常分类（需要人工处理）");
        Console.WriteLine();
        
        Console.WriteLine("=== 基本使用示例完成 ===");
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}
