# Jud4Script.dll 部署和使用说明

## 概述
Jud4Script.dll 是为VisionMaster开发的动态判定库，实现了基于配置文件的检测项目判定功能。

## 文件清单
- `Jud4Script.dll` - 主要动态库文件
- `Newtonsoft.Json.dll` - JSON处理依赖库（版本********）
- 配置文件：`D:\LvConfig\产品型号\{modelNo}.json`

## 部署步骤

### 1. 文件部署
将以下文件放在同一目录下：
- `Jud4Script.dll`
- `Newtonsoft.Json.dll`

### 2. 配置文件准备
1. 创建目录：`D:\LvConfig\产品型号\`
2. 为每个产品型号创建对应的JSON配置文件
3. 确保配置文件使用UTF-8编码保存

### 3. 配置文件格式
```json
{
  "配置版本": "1.0",
  "更新时间": "2025-07-11 00:30:00",
  "说明": "质量检测配置参数",
  "型号名称": "产品型号",
  "片数": "3",
  "检测项目列表": [
    {
      "检测项目": "外圆面伤",
      "分属相机": "相机1",
      "严重分类": "4",
      "轻微分类": "3",
      "良品分类": "0",
      "轻微上限": 99999.0,
      "良品上限": 200.0,
      "良品下限": 0.0,
      "轻微下限": 200.0,
      "偏移量": 0.0,
      "预处理模式": ""
    }
  ]
}
```

### 4. 配置约束验证
确保配置文件满足以下约束：
- 良品上限 ≥ 良品下限
- 轻微上限 ≥ 良品上限  
- 轻微下限 ≤ 良品下限
- 良品分类、轻微分类、严重分类字段值不重复
- 分类字段取值范围：'1'-'9'（字符型）

## 使用方法

### 在VisionMaster脚本中使用
```csharp
using Jud4Script;

// 调用判定方法
string result = JudgmentEngine.Evaluate(modelNo, inspecInfo);
```

### 参数说明
- `modelNo`: 产品型号（字符串）
- `inspecInfo`: 检测信息字符串，格式如："CAM=C5;TS=;JD=;;Items=;外圆面伤=180.0;外圆纹路=0.0;槽内多铜=0.0;"
- 返回值: 出料分类（字符串），可能的值：'0'（异常）、'1'-'9'（根据配置）

### 输入格式详解
检测信息字符串格式：
- 以分号分隔的字符串，包含多个字段
- 检测结果以"Items="字段值开头，后面跟着检测项及对应数值
- 每个检测项格式："检测项目名称=测量值"
- 示例：`"CAM=C5;TS=;JD=;;Items=;外圆面伤=180.0;外圆纹路=0.0;槽内多铜=0.0;"`
- 支持多值格式："检测项目名称*n=值1,值2,值3"（n为测量值数量）

## 返回值说明
- `"1"` - 良品分类（所有检测项OK）
- `"2"/"3"/"4"` - 轻微/严重分类（根据配置文件中的分类字段）
- `"0"` - 异常分类（配置错误、输入错误、测量值无效等）

## 故障排除

### 常见问题
1. **依赖库缺失**
   - 确保Newtonsoft.Json.dll与Jud4Script.dll在同一目录
   - 使用VisionMaster兼容的版本（********）

2. **配置文件编码问题**
   - 确保JSON文件使用UTF-8编码保存
   - 避免中文字符乱码

3. **配置验证失败**
   - 检查数值约束是否满足设计要求
   - 确保分类字段值不重复且在有效范围内

4. **输入格式错误**
   - 检查检测项个数是否与声明一致
   - 确保检测项名称与配置文件完全匹配
   - 验证测量值格式是否正确

### 调试方法
如果返回值为"0"（异常），可能的原因：
- 配置文件不存在或格式错误
- 输入字符串格式不正确
- 检测项个数不匹配
- 测量值无效或为空
- 配置验证失败

## 测试验证
项目包含完整的测试程序`TestJud4Script.exe`，可用于验证功能：
- 正常判定测试
- 异常输入处理测试
- 边界值测试
- 配置验证测试

## 版本信息
- 版本：1.0
- 开发日期：2025年7月
- 兼容性：VisionMaster 4.3.0+, .NET Framework 4.0+
- 依赖：Newtonsoft.Json ********
