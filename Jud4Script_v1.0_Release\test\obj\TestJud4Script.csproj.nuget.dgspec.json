{"format": 1, "restore": {"E:\\BaiduSyncdisk\\软件研发\\Topic\\Jud4Script\\Jud4Script_v1.0_Release\\test\\TestJud4Script.csproj": {}}, "projects": {"E:\\BaiduSyncdisk\\软件研发\\Topic\\Jud4Script\\Jud4Script_v1.0_Release\\test\\TestJud4Script.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\BaiduSyncdisk\\软件研发\\Topic\\Jud4Script\\Jud4Script_v1.0_Release\\test\\TestJud4Script.csproj", "projectName": "TestJud4Script", "projectPath": "E:\\BaiduSyncdisk\\软件研发\\Topic\\Jud4Script\\Jud4Script_v1.0_Release\\test\\TestJud4Script.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\BaiduSyncdisk\\软件研发\\Topic\\Jud4Script\\Jud4Script_v1.0_Release\\test\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"targetAlias": "net48", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win7-x86": {"#import": []}}}}}