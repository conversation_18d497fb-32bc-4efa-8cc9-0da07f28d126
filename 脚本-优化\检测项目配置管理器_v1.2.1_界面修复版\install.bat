@echo off
chcp 65001 >nul
title 检测项目配置管理器 - 安装程序

echo ========================================
echo 检测项目配置管理器 v1.2.1 安装程序
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [信息] 检测到管理员权限
) else (
    echo [警告] 建议以管理员身份运行以获得最佳体验
)

echo.
echo 请选择安装位置：
echo 1. 安装到 Program Files (推荐)
echo 2. 安装到当前用户目录
echo 3. 安装到自定义位置
echo 4. 退出安装
echo.
set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" goto install_program_files
if "%choice%"=="2" goto install_user
if "%choice%"=="3" goto install_custom
if "%choice%"=="4" goto exit
goto invalid_choice

:install_program_files
set "install_path=%ProgramFiles%\检测项目配置管理器"
goto confirm_install

:install_user
set "install_path=%USERPROFILE%\AppData\Local\检测项目配置管理器"
goto confirm_install

:install_custom
echo.
set /p install_path=请输入安装路径: 
if "%install_path%"=="" goto install_custom
goto confirm_install

:confirm_install
echo.
echo 安装路径: %install_path%
echo.
set /p confirm=确认安装? (Y/N): 
if /i "%confirm%"=="Y" goto do_install
if /i "%confirm%"=="N" goto exit
goto confirm_install

:do_install
echo.
echo [信息] 开始安装...

:: 创建安装目录
if not exist "%install_path%" (
    mkdir "%install_path%" 2>nul
    if errorlevel 1 (
        echo [错误] 无法创建安装目录，请检查权限
        pause
        goto exit
    )
)

:: 复制文件
echo [信息] 复制程序文件...
copy /Y "CfgInspectItems.exe" "%install_path%\" >nul
if errorlevel 1 (
    echo [错误] 复制程序文件失败
    pause
    goto exit
)

copy /Y "README.txt" "%install_path%\" >nul
copy /Y "uninstall.bat" "%install_path%\" >nul

:: 创建桌面快捷方式
echo [信息] 创建桌面快捷方式...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\检测项目配置管理器.lnk'); $Shortcut.TargetPath = '%install_path%\CfgInspectItems.exe'; $Shortcut.WorkingDirectory = '%install_path%'; $Shortcut.Description = '检测项目配置管理器'; $Shortcut.Save()" 2>nul

:: 创建开始菜单快捷方式
echo [信息] 创建开始菜单快捷方式...
if not exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\检测项目配置管理器" (
    mkdir "%APPDATA%\Microsoft\Windows\Start Menu\Programs\检测项目配置管理器" 2>nul
)
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%APPDATA%\Microsoft\Windows\Start Menu\Programs\检测项目配置管理器\检测项目配置管理器.lnk'); $Shortcut.TargetPath = '%install_path%\CfgInspectItems.exe'; $Shortcut.WorkingDirectory = '%install_path%'; $Shortcut.Description = '检测项目配置管理器'; $Shortcut.Save()" 2>nul

:: 写入卸载信息
echo [信息] 写入卸载信息...
echo set "install_path=%install_path%" > "%install_path%\uninstall_info.bat"

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo 安装位置: %install_path%
echo.
echo 您可以通过以下方式启动程序：
echo - 双击桌面快捷方式
echo - 从开始菜单启动
echo - 直接运行: %install_path%\CfgInspectItems.exe
echo.
set /p launch=是否立即启动程序? (Y/N): 
if /i "%launch%"=="Y" (
    start "" "%install_path%\CfgInspectItems.exe"
)
goto exit

:invalid_choice
echo [错误] 无效选择，请重新输入
goto install_program_files

:exit
echo.
echo 感谢使用检测项目配置管理器！
pause
exit /b 0
