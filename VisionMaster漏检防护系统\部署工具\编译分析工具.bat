@echo off
chcp 65001 >nul
echo ========================================
echo 编译图像分析工具
echo ========================================
echo.

:: 检查编译器
where csc >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到C#编译器
    echo.
    echo 请安装以下任一开发环境：
    echo 1. .NET Framework SDK 4.0 或更高版本
    echo 2. .NET SDK 6.0 或更高版本
    echo 3. Visual Studio (包含C#编译器)
    echo.
    echo 下载地址：
    echo - .NET Framework: https://dotnet.microsoft.com/download/dotnet-framework
    echo - .NET SDK: https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)

echo ✓ 找到C#编译器
echo.

:: 设置源文件路径
set SOURCE_FILE="%~dp0..\核心代码\图像分析工具.cs"
set OUTPUT_FILE="D:\HODD\ImageAnalyzer.exe"

echo 正在编译图像分析工具...
echo 源文件: %SOURCE_FILE%
echo 输出文件: %OUTPUT_FILE%
echo.

:: 创建输出目录
if not exist "D:\HODD" (
    mkdir "D:\HODD"
    echo ✓ 创建输出目录: D:\HODD
)

:: 编译文件
echo 开始编译...
csc /target:exe /out:%OUTPUT_FILE% %SOURCE_FILE%

if %errorlevel% equ 0 (
    echo.
    echo ✅ 编译成功！
    echo 可执行文件已生成: %OUTPUT_FILE%
    echo.
    
    :: 测试运行
    echo 是否立即测试运行分析工具？(Y/N)
    set /p testrun=
    if /i "%testrun%"=="Y" (
        echo.
        echo 正在启动分析工具...
        cd /d "D:\HODD"
        ImageAnalyzer.exe
    )
) else (
    echo.
    echo ❌ 编译失败！
    echo.
    echo 可能的原因：
    echo 1. 源文件路径不正确
    echo 2. 编译器版本不兼容
    echo 3. 缺少必要的.NET Framework
    echo.
    echo 解决方案：
    echo 1. 确认源文件存在: %SOURCE_FILE%
    echo 2. 尝试使用更高版本的.NET Framework
    echo 3. 检查系统环境变量PATH中是否包含csc.exe路径
    echo.
)

echo.
pause
