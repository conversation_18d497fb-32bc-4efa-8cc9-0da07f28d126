# Jud4Script v2.0.0 安装包

## 📦 安装包概述

欢迎使用Jud4Script v2.0.0产品检测判定引擎！本安装包包含完整的功能模块、详细文档、示例代码和测试程序。

## 🚀 快速开始

### 1. 最小化安装
如果您只需要基本功能，请：
1. 将 `核心文件\Jud4Script.dll` 和 `Newtonsoft.Json.dll` 添加到项目引用
2. 将 `配置文件\0523A-F.json` 复制到 `D:\LvConfig\0523A-F\` 目录
3. 开始使用：
   ```csharp
   string result = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7");
   ```

### 2. 完整安装
如果您需要完整功能和文档，请：
1. 解压整个安装包到目标目录
2. 按照 `文档\安装包说明.md` 进行详细配置
3. 运行测试程序验证安装
4. 参考示例代码开始开发

## 📁 目录结构

```
Jud4Script_v2.0.0/
├── 核心文件/                  # 必需文件
│   ├── Jud4Script.dll         # 主要动态库
│   ├── Newtonsoft.Json.dll    # JSON依赖库
│   └── *.cs                   # 源码文件
├── 文档/                      # 详细文档
│   ├── 安装包说明.md          # 完整安装说明
│   ├── API文档.md             # API接口文档
│   ├── 配置说明.md            # 配置文件说明
│   └── 设计要求.txt           # 原始设计要求
├── 示例代码/                  # 使用示例
│   ├── BasicUsage.cs          # 基本使用
│   ├── AdvancedUsage.cs       # 高级使用
│   └── DebugExample.cs        # 调试功能
├── 测试程序/                  # 功能测试
│   ├── TestSingleValueConstraints.exe  # 约束测试
│   └── TestExceptionHandling.exe       # 异常测试
├── 配置文件/                  # 示例配置
│   └── 0523A-F.json           # 示例产品配置
├── 版本信息.txt               # 版本详情
├── 快速开始.txt               # 快速开始指南
└── README.md                  # 本文件
```

## ✨ 主要功能

- **多值测量判定**：支持单值和多值测量，自动选择最差判定结果
- **格式验证**：严格的输入格式验证和约束检查
- **异常处理**：完整的异常处理和详细错误信息
- **调试功能**：详细的调试日志和流程跟踪
- **配置管理**：灵活的产品型号配置管理
- **偏移量校正**：支持测量值偏移量调整

## 🔧 系统要求

- **操作系统**：Windows 7/8/10/11
- **.NET Framework**：4.0 或更高版本
- **开发环境**：Visual Studio 2010+ （如需源码编译）

## 📊 输入格式

### ✅ 正确格式
```
单值：Items=;外圆面伤=180.0
多值：Items=;槽宽*3=0.32,0.31,0.35
混合：Items=;外圆面伤=180.0;槽宽*3=0.32,0.31,0.35
```

### ❌ 错误格式
```
单值*n格式：Items=;外圆面伤*1=180.0  # 返回ERROR
单值多测量值：Items=;外圆面伤=185,179  # 返回ERROR
数量不一致：Items=;槽宽*3=0.32,0.31  # 返回ERROR
```

## 🧪 验证安装

运行测试程序验证安装：
```cmd
cd 测试程序
TestSingleValueConstraints.exe    # 验证约束功能
TestExceptionHandling.exe         # 验证异常处理
```

## 📖 文档说明

- **安装包说明.md**：完整的安装和配置指南
- **API文档.md**：详细的API接口说明
- **配置说明.md**：配置文件格式和参数说明
- **快速开始.txt**：快速上手指南

## 💡 使用建议

1. **开发阶段**：启用调试模式获取详细信息
2. **生产环境**：禁用调试模式提高性能
3. **异常处理**：使用调试信息定位问题
4. **配置管理**：定期备份配置文件

## 🆕 v2.0.0 新特性

- ✅ 新增单值约束验证功能
- ✅ 增强异常处理和错误信息
- ✅ 添加详细调试日志功能
- ✅ 优化错误信息格式
- ✅ 完善测试程序和文档

## 📞 技术支持

如有问题或需要技术支持：
1. 查看调试信息定位问题
2. 参考文档和示例代码
3. 运行测试程序验证环境
4. 联系开发团队获取支持

## 📄 许可证

本软件为专有软件，版权所有。未经授权不得复制、分发或修改。

---

**感谢使用Jud4Script产品检测判定引擎！**
