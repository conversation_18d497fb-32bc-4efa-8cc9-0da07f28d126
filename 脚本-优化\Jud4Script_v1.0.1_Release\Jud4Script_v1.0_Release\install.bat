@echo off
chcp 65001 >nul
echo ==========================================
echo     Jud4Script.dll 自动安装脚本
echo ==========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 管理员权限检查通过
) else (
    echo ❌ 需要管理员权限运行此脚本
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)
echo.

:: 创建配置目录
echo 1. 创建配置目录...
if not exist "D:\LvConfig\产品型号" (
    mkdir "D:\LvConfig\产品型号"
    echo ✅ 配置目录创建成功: D:\LvConfig\产品型号
) else (
    echo ✅ 配置目录已存在: D:\LvConfig\产品型号
)
echo.

:: 复制配置文件示例
echo 2. 复制配置文件示例...
xcopy "config_samples\*.json" "D:\LvConfig\产品型号\" /Y /Q
if %errorLevel% == 0 (
    echo ✅ 配置文件示例复制成功
) else (
    echo ❌ 配置文件示例复制失败
)
echo.

:: 显示DLL文件位置
echo 3. DLL文件位置信息...
echo    Jud4Script.dll 和 Newtonsoft.Json.dll 位于: %~dp0bin\
echo    请将这两个文件复制到您的VisionMaster项目目录中
echo.

:: 运行测试程序
echo 4. 运行安装验证测试...
echo 正在启动测试程序...
echo.
cd /d "%~dp0test"
copy "..\bin\*.dll" . >nul 2>&1
TestJud4Script.exe
cd /d "%~dp0"
echo.

echo ==========================================
echo 安装完成！
echo.
echo 下一步操作：
echo 1. 将 bin\ 目录中的 DLL 文件复制到您的项目目录
echo 2. 根据需要修改 D:\LvConfig\产品型号\ 中的配置文件
echo 3. 在VisionMaster中使用 JudgmentEngine.Evaluate() 方法
echo.
echo 详细使用说明请参考 docs\ 目录中的文档
echo ==========================================
pause
