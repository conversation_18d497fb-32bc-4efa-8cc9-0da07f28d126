using System;
using Newtonsoft.Json;

namespace Jud4Script
{
    /// <summary>
    /// 用于精确匹配JSON对象结构的强类型模型。
    /// 字段名来自设计要求文档。
    /// </summary>
    public class InspecItemConfig
    {
        [JsonProperty("检测项目")]
        public string ItemName { get; set; }

        [JsonProperty("分属相机")]
        public string Camera { get; set; }

        [JsonProperty("严重分类")]
        public string SevereCategory { get; set; }

        [JsonProperty("轻微分类")]
        public string LightCategory { get; set; }

        [JsonProperty("良品分类")]
        public string GoodCategory { get; set; }

        [JsonProperty("轻微上限")]
        public float LightUpper { get; set; }

        [JsonProperty("良品上限")]
        public float GoodUpper { get; set; }

        [JsonProperty("良品下限")]
        public float GoodLower { get; set; }

        [JsonProperty("轻微下限")]
        public float LightLower { get; set; }

        [JsonProperty("偏移量")]
        public float Offset { get; set; }

        [JsonProperty("预处理模式")]
        public string PreprocessingMode { get; set; }
    }
}
