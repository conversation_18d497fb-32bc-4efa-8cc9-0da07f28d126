// // test12.cs
// using System;
// using System.Linq;

// public static class Pro2
// {
//     public static void Count1()
//     {
//         // 安全计算，防止溢出
//         try 
//         {
//             var numbers = Enumerable.Range(1, 1_000_000);
//             var array = numbers.Where(n => n % 2 == 0).Take(10000).ToArray(); // 限制数据量
//             Console.WriteLine($"Count: {array.Length}, Sum: {array.Sum()}");
//         }
//         catch (OverflowException)
//         {
//             Console.WriteLine("计算溢出，请使用long类型处理大数");
//         }
//     }
// }