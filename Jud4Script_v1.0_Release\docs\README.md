# Jud4Script.dll - VisionMaster判定动态库

## 项目简介
Jud4Script.dll 是为VisionMaster开发的C#动态库，实现基于配置文件的检测项目判定功能。支持多检测项的综合判定，输出分类结果用于产品分拣。

## 功能特性
- ✅ 基于JSON配置文件的灵活判定规则
- ✅ 支持OK/NG1/NG2/ERROR四级判定
- ✅ 良品/轻微/严重/异常四类分类输出
- ✅ 测量值偏移量校正功能
- ✅ 预处理模式预留字段（未来扩展）
- ✅ 线程安全的配置缓存机制
- ✅ 完善的输入验证和异常处理
- ✅ 与VisionMaster完全兼容

## 快速开始

### 1. 环境要求
- .NET Framework 4.0+
- VisionMaster 4.3.0+
- Newtonsoft.Json ********

### 2. 安装部署
1. 将`Jud4Script.dll`和`Newtonsoft.Json.dll`放在同一目录
2. 创建配置目录：`D:\LvConfig\产品型号\`
3. 为每个产品型号创建对应的JSON配置文件

### 3. 基本使用
```csharp
using Jud4Script;

// 调用判定方法
string result = JudgmentEngine.Evaluate("Sample00", "Items-3;外圆面伤V180.0;外圆纹路V0.0;槽内多铜V0.0");
// 返回值：'1'(良品) / '2','3','4'(轻微/严重) / '0'(异常)
```

## 配置文件格式
```json
[
  {
    "检测项目": "外圆面伤",
    "分属相机": "相机1", 
    "严重分类": "4",
    "轻微分类": "3",
    "良品分类": "1",
    "轻微上限": 99999.0,
    "良品上限": 200.0,
    "良品下限": 0.0,
    "轻微下限": 0.0,
    "偏移量": 0.0,
    "预处理模式": ""
  }
]
```

## 判定逻辑
1. **单项判定**：
   - 偏移量校正：调整后测量值 = 原始测量值 + 偏移量
   - OK：调整后测量值在良品范围内
   - NG1：调整后测量值在轻微范围内但不在良品范围内
   - NG2：调整后测量值超出轻微范围
   - ERROR：配置缺失或测量值无效

2. **综合分类**：
   - 良品：所有检测项都为OK
   - 轻微：有任意检测项为NG1
   - 严重：有任意检测项为NG2  
   - 异常：有任意检测项为ERROR

## 文件结构
```
Jud4Script/
├── README.md                 # 项目说明
├── ReqOnJud2Dll.txt         # 设计要求文档
├── 部署和使用说明.md          # 详细使用指南
├── 问题解决记录.md           # 开发问题记录
├── Jud4Script.dll           # 主要动态库
├── Newtonsoft.Json.dll      # JSON依赖库
├── TestJud4Script.exe       # 测试程序
├── InspecItemConfig.cs      # 配置类定义
├── ConfigManager.cs         # 配置管理器
├── JudgmentEngine.cs        # 判定引擎
└── Sample00-优化的对象数组格式.json  # 配置文件示例
```

## 测试验证
运行`TestJud4Script.exe`进行功能验证：
- ✅ 正常判定测试
- ✅ 异常输入处理
- ✅ 边界值测试
- ✅ 配置验证测试

## 技术特点
- **高性能**：配置文件缓存，避免重复读取
- **线程安全**：使用ConcurrentDictionary实现并发安全
- **容错性强**：完善的异常处理和输入验证
- **易维护**：清晰的代码结构和详细的文档

## 版本历史
- **v1.2** (2025-07-09)
  - 新增预处理模式预留字段
  - 支持配置文件格式扩展
  - 更新文档和测试程序
  - 保持向后兼容性

- **v1.1** (2025-07-09)
  - 新增偏移量校正功能
  - 支持测量值自动校正
  - 更新配置文件格式
  - 完善测试验证

- **v1.0** (2025-07-03)
  - 初始版本发布
  - 实现基础判定功能
  - 解决依赖和编码问题
  - 通过完整测试验证

## 支持与反馈
如遇到问题，请参考：
1. [部署和使用说明.md](部署和使用说明.md) - 详细使用指南
2. [问题解决记录.md](问题解决记录.md) - 常见问题解决方案
3. [ReqOnJud2Dll.txt](ReqOnJud2Dll.txt) - 原始设计要求

## 许可证
本项目为内部开发项目，版权归属相关开发团队。
