# Jud4Script v2.0.0 安装包说明

## 📦 安装包概述

本安装包包含Jud4Script产品检测判定引擎的完整版本，具备多值测量、格式验证、异常处理和详细调试功能。

## 📁 安装包内容

```
Jud4Script_v2.0.0/
├── 核心文件/
│   ├── Jud4Script.dll          # 主要动态库文件
│   ├── Newtonsoft.Json.dll     # JSON处理依赖库
│   ├── InspecItemConfig.cs     # 配置类源码
│   ├── ConfigManager.cs        # 配置管理器源码
│   └── JudgmentEngine.cs       # 判定引擎源码
├── 文档/
│   ├── 安装包说明.md           # 本文件
│   ├── API文档.md              # 详细API文档
│   ├── 配置说明.md             # 配置文件说明
│   └── 设计要求.txt            # 原始设计要求
├── 示例代码/
│   ├── BasicUsage.cs           # 基本使用示例
│   ├── AdvancedUsage.cs        # 高级使用示例
│   └── DebugExample.cs         # 调试功能示例
├── 测试程序/
│   ├── TestBasicFunctions.exe      # 基本功能测试
│   ├── TestSingleValueConstraints.exe  # 单值约束测试
│   ├── TestExceptionHandling.exe   # 异常处理测试
│   └── 测试源码/
│       ├── TestBasicFunctions.cs
│       ├── TestSingleValueConstraints.cs
│       └── TestExceptionHandling.cs
└── 配置文件/
    └── 0523A-F.json            # 示例配置文件
```

## 🚀 安装步骤

### 1. 环境要求
- **操作系统**：Windows 7/8/10/11
- **.NET Framework**：4.0 或更高版本
- **开发环境**：Visual Studio 2010 或更高版本（如需源码编译）

### 2. 安装方法

#### 方法一：直接使用DLL（推荐）
1. 解压安装包到目标目录
2. 将 `Jud4Script.dll` 和 `Newtonsoft.Json.dll` 复制到项目目录
3. 在项目中添加对这两个DLL的引用
4. 配置产品型号配置文件
5. 开始使用

#### 方法二：源码编译
1. 解压安装包到目标目录
2. 使用Visual Studio打开源码文件
3. 编译生成 `Jud4Script.dll`
4. 按方法一的步骤3-5继续

### 3. 配置文件设置
1. 创建配置目录：`D:\LvConfig\产品型号\`
2. 将示例配置文件 `0523A-F.json` 复制到该目录
3. 根据实际需求修改配置文件
4. 为其他产品型号创建对应的配置文件

## 🔧 快速开始

### 基本使用示例
```csharp
using Jud4Script;

class Program
{
    static void Main()
    {
        // 基本判定
        string result = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7");
        Console.WriteLine("判定结果: " + result);
        
        // 多值判定
        string result2 = JudgmentEngine.Evaluate("0523A-F", "Items=;槽宽*3=0.32,0.31,0.35");
        Console.WriteLine("多值判定结果: " + result2);
    }
}
```

### 调试功能示例
```csharp
// 启用调试模式
JudgmentEngine.SetDebugEnabled(true);

// 执行判定
string result = JudgmentEngine.Evaluate("0523A-F", "Items=;槽深=0.7");

// 获取详细调试信息
string debugInfo = JudgmentEngine.GetDebugInfo();
Console.WriteLine("调试信息:");
Console.WriteLine(debugInfo);

// 清空调试信息
JudgmentEngine.ClearDebugInfo();
```

## 📖 输入格式规范

### 标准格式
```
"CAM=相机编号;TS=时间戳;PD=预判定;POF=;Items=;检测项1=测量值1;检测项2*n=测量值1,测量值2,...;..."
```

### 格式约束（v2.0.0新增）
1. **单值格式**：`检测项=测量值`
   - ✅ 正确：`外圆面伤=180.0`
   - ❌ 错误：`外圆面伤*1=180.0`（单值不允许*n格式）
   - ❌ 错误：`外圆面伤=185,179`（单值不允许多个测量值）

2. **多值格式**：`检测项*n=值1,值2,...`
   - ✅ 正确：`槽宽*3=0.32,0.31,0.35`
   - ❌ 错误：`槽宽*3=0.32,0.31`（数量不一致）

## 🧪 测试验证

### 运行测试程序
1. **基本功能测试**：
   ```cmd
   TestBasicFunctions.exe
   ```
   验证基本判定功能和多值处理

2. **单值约束测试**：
   ```cmd
   TestSingleValueConstraints.exe
   ```
   验证单值格式约束和错误处理

3. **异常处理测试**：
   ```cmd
   TestExceptionHandling.exe
   ```
   验证异常处理和调试信息输出

### 预期测试结果
- 所有正确格式应返回相应的判定结果（0-9）
- 所有错误格式应返回"0"（ERROR）
- 调试模式下应输出详细的处理流程信息

## 🔍 故障排除

### 常见问题

1. **"找不到配置文件"错误**
   - 检查配置文件路径：`D:\LvConfig\产品型号\{产品型号}.json`
   - 确保配置文件格式正确
   - 检查产品型号是否匹配

2. **"输入格式错误"**
   - 检查输入字符串是否包含"Items="字段
   - 验证检测项格式是否符合约束规则
   - 使用调试模式查看详细错误信息

3. **"检测项不存在"错误**
   - 检查配置文件中是否包含该检测项
   - 确认检测项名称拼写正确
   - 查看调试信息中的可用配置项列表

### 调试方法
```csharp
// 启用调试模式获取详细信息
JudgmentEngine.SetDebugEnabled(true);
string result = JudgmentEngine.Evaluate("产品型号", "检测信息");
string debugInfo = JudgmentEngine.GetDebugInfo();
Console.WriteLine(debugInfo);
```

## 📞 技术支持

如遇到问题或需要技术支持，请：
1. 首先查看调试信息定位问题
2. 检查配置文件和输入格式
3. 运行测试程序验证环境
4. 联系开发团队获取支持

## 📄 版本信息

- **版本号**：v2.0.0
- **发布日期**：2025-01-12
- **兼容性**：向后兼容v1.x版本

### 主要更新内容
- ✅ 新增单值约束验证功能
- ✅ 增强异常处理和错误信息
- ✅ 添加详细调试日志功能
- ✅ 优化错误信息格式
- ✅ 完善测试程序和文档
- ✅ 提供完整的安装包

## 📜 许可证

本软件为专有软件，版权所有。未经授权不得复制、分发或修改。
