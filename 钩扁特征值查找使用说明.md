# VisionMaster 4.3.0 钩扁特征值查找脚本使用说明

## 概述

本脚本用于在VisionMaster 4.3.0中实现钩外径算法的特征值匹配和图像存储功能。脚本接收外部输入的角度(dangle)和距离(dis)参数，进行特征值匹配，并将匹配的图像存储到本地。

## 文件说明

### 1. 基础版本
- **文件名**: `C5钩扁特征值查找.cs`
- **功能**: 基本的特征值匹配和图像存储
- **特点**: 简单易用，适合基本需求

### 2. 增强版本
- **文件名**: `C5钩扁特征值查找_增强版.cs`
- **功能**: 完整的特征值匹配、图像存储、配置管理和数据持久化
- **特点**: 功能完整，支持配置文件，数据可持久化

### 3. 配置文件
- **文件名**: `HookFeatureConfig.xml`
- **功能**: 系统配置参数设置
- **位置**: 与脚本文件同目录

## 输入参数

### dangle (string)
- **描述**: 钩外径算法获取的角度参数
- **格式**: 分号分隔的数值字符串
- **示例**: `"98.893;128.893;158.892;188.893;218.893;248.893;278.894;308.892;338.894;8.895;38.894;68.894"`

### dis (string)
- **描述**: 钩外径算法获取的距离参数
- **格式**: 分号分隔的数值字符串
- **示例**: `"385.195;385.079;384.910;385.118;385.148;385.005;385.397;384.984;385.740;386.011;385.533;385.492"`

### pic (IMAGE)
- **描述**: VisionMaster的IMAGE类型图像对象
- **格式**: VM内部IMAGE对象
- **用途**: 用于保存到本地文件系统

## 功能特性

### 1. 特征值解析
- 自动解析分号分隔的特征值字符串
- 支持角度和距离两种类型的特征值
- 错误处理和格式验证

### 2. 相似度计算
- 基于角度和距离的综合相似度算法
- 可配置的匹配阈值
- 支持权重调整（默认各占50%）

### 3. 图像存储
- 自动创建存储目录
- 基于日期时间的文件命名
- 支持多种图像格式
- 可配置的存储路径

### 4. 匹配查找
- 在历史特征值中查找相似项
- 按相似度排序显示结果
- 可配置的显示数量限制

### 5. 数据持久化（增强版）
- XML格式的数据库存储
- 自动加载历史数据
- 数据量控制和清理

### 6. 日志记录（增强版）
- 详细的操作日志
- 可配置的日志级别
- 错误追踪和调试支持

## 配置参数说明

### 匹配设置
- **AngleThreshold**: 角度匹配阈值（默认5.0度）
- **DistanceThreshold**: 距离匹配阈值（默认10.0像素）
- **SimilarityThreshold**: 相似度阈值（默认0.8）
- **MaxDisplayMatches**: 最大显示匹配数量（默认5个）

### 图像存储设置
- **RootDirectory**: 图像存储根目录（默认D:\HookImages）
- **ImageFormat**: 图像格式（默认bmp）
- **CreateDateFolders**: 是否创建日期文件夹（默认true）
- **FileNamePrefix**: 文件名前缀（默认Hook_）

### 数据库设置
- **EnablePersistence**: 是否启用持久化（默认true）
- **DatabaseFile**: 数据库文件路径
- **MaxFeatureCount**: 最大特征值数量（默认10000）

### 日志设置
- **EnableLogging**: 是否启用日志（默认true）
- **LogFile**: 日志文件路径
- **LogLevel**: 日志级别

## 使用步骤

### 1. 部署脚本
1. 将脚本文件复制到VisionMaster项目目录
2. 如使用增强版，同时复制配置文件
3. 根据需要修改配置参数

### 2. 配置输入
1. 在VM流程中设置dangle输入变量
2. 在VM流程中设置dis输入变量
3. 在VM流程中设置pic输入变量

### 3. 运行脚本
1. 脚本会自动解析输入参数
2. 进行特征值匹配
3. 保存图像到本地
4. 显示匹配结果

### 4. 查看结果
1. 检查弹出的匹配结果对话框
2. 查看保存的图像文件
3. 查看日志文件（增强版）

## 目录结构

```
D:\HookImages\                    # 图像存储根目录
├── 2024-01-15\                  # 日期文件夹
│   ├── Hook_143052_123.bmp      # 保存的图像文件
│   └── Hook_143055_456.bmp
├── 2024-01-16\
│   └── ...
├── FeatureDatabase.xml          # 特征值数据库（增强版）
└── Logs\                        # 日志目录（增强版）
    └── HookFeature.log          # 日志文件
```

## 注意事项

### 1. 系统要求
- VisionMaster 4.3.0或更高版本
- .NET Framework 4.6.1或更高版本
- 足够的磁盘空间用于图像存储

### 2. 性能考虑
- 特征值数据库会随时间增长，建议定期清理
- 大量图像存储需要考虑磁盘空间
- 匹配算法的时间复杂度与历史数据量成正比

### 3. 错误处理
- 输入参数验证
- 文件系统错误处理
- 图像保存失败处理
- 数据库操作异常处理

### 4. 扩展建议
- 可根据实际需求调整相似度算法
- 可添加更多的特征值类型
- 可实现网络化的特征值共享
- 可添加图像预处理功能

## 技术支持

如遇到问题，请检查：
1. 输入参数格式是否正确
2. 配置文件是否存在且格式正确
3. 存储目录是否有写入权限
4. 日志文件中的错误信息

## 版本历史

- **v1.0**: 基础版本，实现基本功能
- **v2.0**: 增强版本，添加配置管理和数据持久化
