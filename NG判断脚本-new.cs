using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Script.Methods;
using Newtonsoft.Json;

/************************************
Shell Module default code: using .NET Framework 4.6.1
*************************************/
public partial class UserScript : ScriptMethods, IProcessMethods
{
    int processCount;

    public void Init()
    {
        processCount = 0;
    }

    public bool Process()
    {
        // 假设 -- 是 ScriptMethods 中定义的字段
                
        if(exImg != "")
	        judgeMnt = Evaluate(modelno, itemsSv, resInfo);
        else
        	judgeMnt = -9;
               
        return true;
    }

    public static int Evaluate(string modelNo, string inspecItems, string resultInfo)
    {
    	int judgeFin = 0;
    	
        string logFilePath = @"D:\logs\script_output5_l.log";
        using (StreamWriter sw = new StreamWriter(logFilePath, true, Encoding.UTF8))
        {
            string jsonPath = @"D:\Config Parameters\品番管理\" + modelNo + @"\检测项目和限度.json";

            if (!File.Exists(jsonPath))
            {
                sw.WriteLine("配置文件不存在：" + jsonPath);
                sw.Flush();
                Console.WriteLine("配置文件不存在：" + jsonPath);
                return -1;
            }

            string jsonContent = File.ReadAllText(jsonPath);

            List<object[]> configData = null;

            try
            {
                configData = JsonConvert.DeserializeObject<List<object[]>>(jsonContent);
                sw.WriteLine("反序列化成功，条目数量：" + configData.Count);
            }
            catch (Exception ex)
            {
                sw.WriteLine("反序列化失败，异常信息：");
                sw.WriteLine("类型：" + ex.GetType().ToString());
                sw.WriteLine("消息：" + ex.Message);
                sw.WriteLine("堆栈跟踪：" + ex.StackTrace);
                sw.Flush();
                Console.WriteLine("反序列化失败：" + ex.Message);
                return -2;
            }

            string[] fieldNames = {
                "检测项目", "分属相机", "严重分类", "轻微分类", "良品分类",
                "轻微上限", "良品上限", "良品下限", "轻微下限", "测量值",
                "判定类型", "严重数量", "严重比例", "轻微数量", "轻微比例",
                "合计数量", "合计比例"
            };

            Dictionary<string, List<string>> resultDict = new Dictionary<string, List<string>>();
            string[] resultParts = resultInfo.Split(';');

            bool isItemSection = false;
            foreach (string part in resultParts)
            {
                string trimmedPart = part.Trim();
                if (trimmedPart.StartsWith("Items-"))
                {
                    isItemSection = true;
                    continue;
                }

                if (isItemSection && trimmedPart.Contains("V"))
                {
                    string[] itemAndValue = trimmedPart.Split('V');
                    if (itemAndValue.Length == 2)
                    {
                        string itemName = itemAndValue[0];
                        string valueStr = itemAndValue[1];
                        List<string> values = valueStr.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                        resultDict[itemName] = values;
                        sw.WriteLine(itemName + " -> 数组长度: " + values.Count + ", 值: " + string.Join(", ", values));
                    }
                }
            }

            // 后续处理逻辑保持不变，但需要循环处理每个数组元素
            string[] itemsToCheck = inspecItems.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (string itemName in itemsToCheck)
            {
                object[] itemConfig = null;

                foreach (object[] row in configData)
                {
                    if (row[0] != null && row[0].ToString() == itemName)
                    {
                        itemConfig = row;
                        break;
                    }
                }

                if (itemConfig == null)
                {
                    Console.WriteLine("未找到检测项目配置：" + itemName);
                    sw.WriteLine("未找到检测项目配置：" + itemName);
                    judgeFin = -3;
                    continue;
                }

                List<string> measureValues;
                if (!resultDict.TryGetValue(itemName, out measureValues) || measureValues == null || measureValues.Count == 0)
                {
                    Console.WriteLine("未找到检测项的测量值或测量值为空：" + itemName);
                    sw.WriteLine("未找到检测项的测量值或测量值为空：" + itemName);
                    judgeFin = -4;
                    continue;
                }

                string measureValueStr = measureValues[0]; // 取数组第一个元素
                float measureValue;
                if (!float.TryParse(measureValueStr, out measureValue))
                {
                    Console.WriteLine("测量值无效：" + itemName + " -> " + measureValueStr);
                    sw.WriteLine("测量值无效：" + itemName + " -> " + measureValueStr);
                    judgeFin = -5;
                    continue;
                }

				float goodUpper = 0;
				float goodLower = 0;
				float lightUpper = 0;
				float lightLower = 0;
				
				bool configError = false;
				
				// 提取良品上限
				if (itemConfig[6] != null)
				{
				    if (!float.TryParse(itemConfig[6].ToString(), out goodUpper))
				    {
				        sw.WriteLine("良品上限格式错误：" + itemConfig[6].ToString());
				        configError = true;
				    }
				}
				else
				{
				    sw.WriteLine("良品上限未配置");
				    configError = true;
				}
				
				// 提取良品下限
				if (itemConfig[7] != null)
				{
				    if (!float.TryParse(itemConfig[7].ToString(), out goodLower))
				    {
				        sw.WriteLine("良品下限格式错误：" + itemConfig[5].ToString());
				        configError = true;
				    }
				}
				else
				{
				    sw.WriteLine("良品下限未配置");
				    configError = true;
				}
				
				// 提取轻微上限
				if (itemConfig[5] != null)
				{
				    if (!float.TryParse(itemConfig[5].ToString(), out lightUpper))
				    {
				        sw.WriteLine("轻微上限格式错误：" + itemConfig[7].ToString());
				        configError = true;
				    }
				}
				else
				{
				    sw.WriteLine("轻微上限未配置");
				    configError = true;
				}
				
				// 提取轻微下限
				if (itemConfig[8] != null)
				{
				    if (!float.TryParse(itemConfig[8].ToString(), out lightLower))
				    {
				        sw.WriteLine("轻微下限格式错误：" + itemConfig[8].ToString());
				        configError = true;
				    }
				}
				else
				{
				    sw.WriteLine("轻微下限未配置");
				    configError = true;
				}
				
				// 如果配置有误，直接视为 NG2，不进行判断
				string judgment = "OK";
				if (configError)
				{
				    judgment = "NG2";
				    judgeFin = -6;

				}
				else if (!(measureValue >= goodLower && measureValue <= goodUpper))
				{
				    if (measureValue >= lightLower && measureValue <= lightUpper)
				    {
				        judgment = "NG1";
				        judgeFin = 1;

				    }
				    else
				    {
				        judgment = "NG2";
				        judgeFin = 2;

				    }
				}
				
                Console.WriteLine(itemName + " -> " + judgment);
                sw.WriteLine(itemName + " -> " + judgment);
            }

            sw.Flush();
            return judgeFin;
        }
    }
}
