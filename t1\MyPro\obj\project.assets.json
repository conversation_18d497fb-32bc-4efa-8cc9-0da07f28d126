{"version": 3, "targets": {"net8.0": {"HtmlAgilityPack/1.12.1": {"type": "package", "compile": {"lib/net8.0/HtmlAgilityPack.dll": {"related": ".deps.json;.pdb;.xml"}}, "runtime": {"lib/net8.0/HtmlAgilityPack.dll": {"related": ".deps.json;.pdb;.xml"}}}}}, "libraries": {"HtmlAgilityPack/1.12.1": {"sha512": "SP6/2Y26CXtxjXn0Wwsom9Ek35SNWKHEu/IWhNEFejBSSVWWXPRSlpqpBSYWv1SQhYFnwMO01xVbEdK3iRR4hg==", "type": "package", "path": "htmlagilitypack/1.12.1", "files": [".nupkg.metadata", ".signature.p7s", "htmlagilitypack.1.12.1.nupkg.sha512", "htmlagilitypack.nuspec", "lib/Net35/HtmlAgilityPack.dll", "lib/Net35/HtmlAgilityPack.pdb", "lib/Net35/HtmlAgilityPack.xml", "lib/Net40-client/HtmlAgilityPack.dll", "lib/Net40-client/HtmlAgilityPack.pdb", "lib/Net40-client/HtmlAgilityPack.xml", "lib/Net40/HtmlAgilityPack.XML", "lib/Net40/HtmlAgilityPack.dll", "lib/Net40/HtmlAgilityPack.pdb", "lib/Net45/HtmlAgilityPack.XML", "lib/Net45/HtmlAgilityPack.dll", "lib/Net45/HtmlAgilityPack.pdb", "lib/NetCore45/HtmlAgilityPack.XML", "lib/NetCore45/HtmlAgilityPack.dll", "lib/NetCore45/HtmlAgilityPack.pdb", "lib/net8.0/HtmlAgilityPack.deps.json", "lib/net8.0/HtmlAgilityPack.dll", "lib/net8.0/HtmlAgilityPack.pdb", "lib/net8.0/HtmlAgilityPack.xml", "lib/netstandard2.0/HtmlAgilityPack.deps.json", "lib/netstandard2.0/HtmlAgilityPack.dll", "lib/netstandard2.0/HtmlAgilityPack.pdb", "lib/netstandard2.0/HtmlAgilityPack.xml", "lib/portable-net45+netcore45+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.XML", "lib/portable-net45+netcore45+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.dll", "lib/portable-net45+netcore45+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.pdb", "lib/portable-net45+netcore45+wpa81+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.XML", "lib/portable-net45+netcore45+wpa81+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.dll", "lib/portable-net45+netcore45+wpa81+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.pdb", "lib/uap10.0/HtmlAgilityPack.XML", "lib/uap10.0/HtmlAgilityPack.dll", "lib/uap10.0/HtmlAgilityPack.pdb", "lib/uap10.0/HtmlAgilityPack.pri", "readme.md"]}}, "projectFileDependencyGroups": {"net8.0": ["HtmlAgilityPack >= 1.12.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\test\\t1\\MyPro\\MyPro.csproj", "projectName": "MyPro", "projectPath": "D:\\test\\t1\\MyPro\\MyPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\test\\t1\\MyPro\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"HtmlAgilityPack": {"target": "Package", "version": "[1.12.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.408/PortableRuntimeIdentifierGraph.json"}}}}