# 缺陷检测参数转换示例

## 📋 概述

本文档展示了如何将VisionMaster中的缺陷检测结果转换为Jud4Script需要的格式。

## 🎯 关键信息

- **C1**: 代表相机1(CAM1)
- **缺陷项目**: 外圆碰伤、外圆纹路、槽内多铜等具体缺陷名称
- **数值**: 缺陷的测量值，通常为0.000格式

## 📊 转换示例

### 示例1：单个缺陷检测

**场景**: 只检测外圆碰伤

```csharp
// VisionMaster输入
modelNo = "0523A-F"
in0 = "1.250"

// 转换过程
// 单个数值默认对应第一个缺陷项目：外圆碰伤

// Jud4Script输出
inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆碰伤=1.250;"

// 判定结果
out0 = "3" // 假设1.250超过良品阈值但在轻微范围内
```

### 示例2：多缺陷检测（键值对格式）

**场景**: 检测多个具体缺陷项目

```csharp
// VisionMaster输入
modelNo = "0523A-F"
in0 = "外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000"

// 转换过程
// 直接使用缺陷名称和对应数值

// Jud4Script输出
inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;"

// 判定逻辑
// - 外圆碰伤=1.250 → 检查配置文件中的阈值
// - 外圆纹路=0.500 → 检查配置文件中的阈值  
// - 槽内多铜=0.000 → 通常为良品

// 判定结果
out0 = "3" // 取最严重的分类
```

### 示例3：数组格式转换

**场景**: VisionMaster输出数组格式的缺陷值

```csharp
// VisionMaster输入
modelNo = "0523A-F"
in0 = "1.250,0.500,0.000,0.200"

// 转换过程
// 按顺序映射到预定义的缺陷项目
// 第1个值 → 外圆碰伤
// 第2个值 → 外圆纹路
// 第3个值 → 槽内多铜
// 第4个值 → 槽深异常

// Jud4Script输出
inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆碰伤=1.250;外圆纹路=0.500;槽内多铜=0.000;槽深异常=0.200;"

// 判定结果
out0 = "3" // 根据各项目的最严重分类
```

### 示例4：分号分隔格式

**场景**: 使用分号分隔的缺陷值

```csharp
// VisionMaster输入
modelNo = "0523A-F"
in0 = "0.000;0.000;0.000"

// 转换过程
// 所有缺陷值都为0.000，表示无缺陷

// Jud4Script输出
inspecInfo = "CAM=C1;TS=;JD=;;Items=;外圆碰伤=0.000;外圆纹路=0.000;槽内多铜=0.000;"

// 判定结果
out0 = "1" // 良品
```

## 🔧 配置文件对应

### 配置文件示例 (0523A-F.json)

```json
{
  "配置版本": "1.0",
  "型号名称": "0523A-F",
  "检测项目列表": [
    {
      "检测项目": "外圆碰伤",
      "分属相机": "相机1",
      "严重分类": "4",
      "轻微分类": "3", 
      "良品分类": "1",
      "轻微上限": 2.000,
      "良品上限": 1.000,
      "良品下限": 0.000,
      "轻微下限": 0.000
    },
    {
      "检测项目": "外圆纹路",
      "分属相机": "相机1",
      "严重分类": "4",
      "轻微分类": "3",
      "良品分类": "1", 
      "轻微上限": 1.000,
      "良品上限": 0.600,
      "良品下限": 0.000,
      "轻微下限": 0.000
    },
    {
      "检测项目": "槽内多铜",
      "分属相机": "相机1",
      "严重分类": "4",
      "轻微分类": "3",
      "良品分类": "1",
      "轻微上限": 0.500,
      "良品上限": 0.200,
      "良品下限": 0.000,
      "轻微下限": 0.000
    }
  ]
}
```

## 📈 判定逻辑示例

### 场景：混合缺陷等级

```csharp
// 输入数据
in0 = "外圆碰伤=1.500;外圆纹路=0.400;槽内多铜=0.100"

// 各项目判定
// 外圆碰伤=1.500 > 1.000(良品上限) 且 ≤ 2.000(轻微上限) → 轻微(3)
// 外圆纹路=0.400 ≤ 0.600(良品上限) → 良品(1)
// 槽内多铜=0.100 ≤ 0.200(良品上限) → 良品(1)

// 最终判定：取最严重等级
out0 = "3" // 轻微分类
```

### 场景：严重缺陷

```csharp
// 输入数据  
in0 = "外圆碰伤=3.000;外圆纹路=0.200;槽内多铜=0.050"

// 各项目判定
// 外圆碰伤=3.000 > 2.000(轻微上限) → 严重(4)
// 外圆纹路=0.200 ≤ 0.600(良品上限) → 良品(1)
// 槽内多铜=0.050 ≤ 0.200(良品上限) → 良品(1)

// 最终判定：取最严重等级
out0 = "4" // 严重分类
```

## 🛠️ 缺陷项目映射表

| 序号 | 缺陷项目名称 | 说明 | 典型阈值范围 |
|------|-------------|------|-------------|
| 1 | 外圆碰伤 | 外圆表面的碰撞损伤 | 0.000-2.000 |
| 2 | 外圆纹路 | 外圆表面的纹理缺陷 | 0.000-1.000 |
| 3 | 槽内多铜 | 槽内多余的铜材料 | 0.000-0.500 |
| 4 | 槽深异常 | 槽的深度不符合要求 | 0.000-1.500 |
| 5 | 槽宽异常 | 槽的宽度不符合要求 | 0.000-1.200 |
| 6 | 表面划伤 | 表面的划痕缺陷 | 0.000-0.800 |
| 7 | 尺寸偏差 | 整体尺寸偏差 | 0.000-2.500 |
| 8 | 形状变形 | 产品形状变形 | 0.000-1.800 |

## ⚠️ 注意事项

1. **缺陷名称匹配**: 确保in0中的缺陷名称与配置文件完全一致
2. **数值格式**: 建议使用3位小数格式(如0.000)
3. **相机编号**: C1代表相机1，如有多个相机需要相应调整
4. **阈值设置**: 根据实际产品要求设置合理的判定阈值
5. **异常处理**: 无效数值会自动设置为0.000

## 🔍 调试建议

1. **启用日志**: 查看D:\HODD\Jud4Script_Log.txt了解转换过程
2. **验证配置**: 确保配置文件路径和格式正确
3. **测试数据**: 使用已知的测试数据验证判定结果
4. **阈值调整**: 根据实际生产需求调整判定阈值

---

**重要提醒**: 
- 缺陷项目名称必须与配置文件中的"检测项目"字段完全匹配
- 数值格式建议保持一致性(如统一使用3位小数)
- 定期验证判定结果的准确性
