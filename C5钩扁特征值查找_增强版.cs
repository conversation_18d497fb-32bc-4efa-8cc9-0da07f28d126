using System;
using System.Text;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using Script.Methods;

/************************************
Shell Module default code: using .NET Framwwork 4.6.1
钩扁特征值查找增强版 - 支持配置文件和数据持久化
特点：无弹窗界面，所有信息通过日志记录

输出变量状态码说明：
matchResult 和 out0 输出相同的状态码值：
  3  : 找到多个匹配（5个或以上）
  2  : 找到少量匹配（2-4个）
  1  : 找到单个匹配
  0  : 处理成功但无匹配
 -1  : 输入参数错误
 -2  : 特征值解析失败
 -3  : 记录失败
-99  : 系统异常错误
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;

    // 输出变量 - 匹配结果状态码
    public int matchResult { get; set; }

    // 外部输出变量 - 兼容性输出
    public int out0 { get; set; }

    // 特征值数据库，存储历史特征值
    private List<FeatureData> featureDatabase;
    
    // 配置参数
    private double angleThreshold = 5.0;  // 角度阈值
    private double distanceThreshold = 10.0;  // 距离阈值
    private double similarityThreshold = 0.8;  // 相似度阈值
    private int maxDisplayMatches = 5;  // 最大显示匹配数量
    
    // 记录配置（替代图像存储）
    private bool enableDetailedLogging = true;  // 是否启用详细日志记录

    // 数据库配置
    private bool enablePersistence = true;
    private string databaseFile = @"D:\HookFC\HookFeatures.txt";
    private int maxFeatureCount = 10000;

    // 日志配置
    private bool enableLogging = true;
    private string logFile = @"D:\HookFC\HookFeature.log";

    /// <summary>
    /// 特征值数据结构
    /// </summary>
    [Serializable]
    public class FeatureData
    {
        public List<double> Angles { get; set; }
        public List<double> Distances { get; set; }
        public string ImagePath { get; set; }
        public DateTime CreateTime { get; set; }
        public double LastSimilarity { get; set; }  // 最后匹配的相似度
        
        public FeatureData()
        {
            Angles = new List<double>();
            Distances = new List<double>();
            CreateTime = DateTime.Now;
            LastSimilarity = 0.0;
        }
    }

    /// <summary>
    /// 特征值数据库容器（用于序列化）
    /// </summary>
    [Serializable]
    public class FeatureDatabase
    {
        public List<FeatureData> Features { get; set; }
        
        public FeatureDatabase()
        {
            Features = new List<FeatureData>();
        }
    }

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;
        matchResult = 0;  // 初始化匹配结果状态码
        out0 = 0;  // 初始化外部输出变量
        featureDatabase = new List<FeatureData>();
        
        // 确保HookFC根目录存在
        EnsureHookFCDirectoryExists();

        // 加载配置文件
        LoadConfiguration();

        // 确保目录存在
        EnsureDirectoriesExist();
        
        // 加载历史特征值数据
        LoadFeatureDatabase();
        
        WriteLog("系统初始化完成");
    }

    /// <summary>
    /// 确保HookFC根目录存在
    /// </summary>
    private void EnsureHookFCDirectoryExists()
    {
        try
        {
            string hookFCDir = @"D:\HookFC";
            if (!Directory.Exists(hookFCDir))
            {
                Directory.CreateDirectory(hookFCDir);
                WriteLog("创建HookFC根目录: " + hookFCDir);
            }

            // 不再需要创建Images目录，改为日志记录

            // 创建默认配置文件（如果不存在）
            string configFile = @"D:\HookFC\HookFeatureConfig.txt";
            if (!File.Exists(configFile))
            {
                CreateDefaultConfigFile(configFile);
            }
        }
        catch (Exception ex)
        {
            WriteLog("创建HookFC目录失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 创建默认配置文件
    /// </summary>
    private void CreateDefaultConfigFile(string configPath)
    {
        try
        {
            string defaultConfig = @"# 钩扁特征值查找配置文件
# 所有文件都将创建在 D:\HookFC 目录下

# 匹配设置
AngleThreshold=5.0
DistanceThreshold=10.0
SimilarityThreshold=0.8
MaxDisplayMatches=10

# 记录设置
EnableDetailedLogging=true

# 数据库设置
EnablePersistence=true
DatabaseFile=HookFeatures.txt
MaxFeatureCount=1000

# 日志设置
EnableLogging=true
LogFile=HookFeature.log";

            File.WriteAllText(configPath, defaultConfig, Encoding.UTF8);
            WriteLog("创建默认配置文件: " + configPath);
        }
        catch (Exception ex)
        {
            WriteLog("创建默认配置文件失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 加载配置文件
    /// </summary>
    private void LoadConfiguration()
    {
        try
        {
            string configFile = @"D:\HookFC\HookFeatureConfig.txt";
            if (File.Exists(configFile))
            {
                string[] lines = File.ReadAllLines(configFile);
                foreach (string line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
                        continue;

                    string[] parts = line.Split('=');
                    if (parts.Length == 2)
                    {
                        string key = parts[0].Trim();
                        string value = parts[1].Trim();

                        switch (key.ToLower())
                        {
                            case "anglethreshold":
                                double angleThresh;
                                if (double.TryParse(value, out angleThresh))
                                    angleThreshold = angleThresh;
                                break;
                            case "distancethreshold":
                                double distThresh;
                                if (double.TryParse(value, out distThresh))
                                    distanceThreshold = distThresh;
                                break;
                            case "similaritythreshold":
                                double simThresh;
                                if (double.TryParse(value, out simThresh))
                                    similarityThreshold = simThresh;
                                break;
                            case "maxdisplaymatches":
                                int maxMatches;
                                if (int.TryParse(value, out maxMatches))
                                    maxDisplayMatches = maxMatches;
                                break;
                            case "enabledetailedlogging":
                                bool detailedLogging;
                                if (bool.TryParse(value, out detailedLogging))
                                    enableDetailedLogging = detailedLogging;
                                break;
                            case "enablepersistence":
                                bool persistence;
                                if (bool.TryParse(value, out persistence))
                                    enablePersistence = persistence;
                                break;
                            case "databasefile":
                                databaseFile = value;
                                break;
                            case "maxfeaturecount":
                                int maxFeatures;
                                if (int.TryParse(value, out maxFeatures))
                                    maxFeatureCount = maxFeatures;
                                break;
                            case "enablelogging":
                                bool logging;
                                if (bool.TryParse(value, out logging))
                                    enableLogging = logging;
                                break;
                            case "logfile":
                                logFile = value;
                                break;
                        }
                    }
                }

                WriteLog("配置文件加载成功");
            }
            else
            {
                WriteLog("配置文件不存在，使用默认配置");
            }
        }
        catch (Exception ex)
        {
            WriteLog("加载配置文件失败: " + ex.Message);
        }
    }



    /// <summary>
    /// 确保必要的目录存在
    /// </summary>
    private void EnsureDirectoriesExist()
    {
        try
        {
            // 不再需要图像存储目录

            // 确保日志文件目录存在
            if (enableLogging)
            {
                string logDir = Path.GetDirectoryName(logFile);
                if (!string.IsNullOrEmpty(logDir) && !Directory.Exists(logDir))
                {
                    Directory.CreateDirectory(logDir);
                    WriteLog("创建日志目录: " + logDir);
                }
            }

            // 确保数据库文件目录存在
            if (enablePersistence)
            {
                string dbDir = Path.GetDirectoryName(databaseFile);
                if (!string.IsNullOrEmpty(dbDir) && !Directory.Exists(dbDir))
                {
                    Directory.CreateDirectory(dbDir);
                    WriteLog("创建数据库目录: " + dbDir);
                }
            }
        }
        catch (Exception ex)
        {
            WriteLog("创建目录失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 写入日志
    /// </summary>
    private void WriteLog(string message)
    {
        if (!enableLogging) return;

        try
        {
            // 确保日志文件目录存在
            string logDir = Path.GetDirectoryName(logFile);
            if (!string.IsNullOrEmpty(logDir) && !Directory.Exists(logDir))
            {
                Directory.CreateDirectory(logDir);
            }

            string logMessage = "[" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "] " + message;
            File.AppendAllText(logFile, logMessage + Environment.NewLine);
        }
        catch
        {
            // 忽略日志写入错误，避免无限递归
        }
    }

    /// <summary>
    /// 加载特征值数据库
    /// </summary>
    private void LoadFeatureDatabase()
    {
        if (!enablePersistence || !File.Exists(databaseFile))
        {
            WriteLog("特征值数据库文件不存在，创建新数据库");
            return;
        }

        try
        {
            string[] lines = File.ReadAllLines(databaseFile);
            featureDatabase = new List<FeatureData>();

            FeatureData currentFeature = null;
            foreach (string line in lines)
            {
                if (line.StartsWith("FEATURE:"))
                {
                    if (currentFeature != null)
                    {
                        featureDatabase.Add(currentFeature);
                    }
                    currentFeature = new FeatureData();
                }
                else if (currentFeature != null)
                {
                    if (line.StartsWith("ANGLES:"))
                    {
                        string angleData = line.Substring(7);
                        currentFeature.Angles = ParseFeatureString(angleData);
                    }
                    else if (line.StartsWith("DISTANCES:"))
                    {
                        string distanceData = line.Substring(10);
                        currentFeature.Distances = ParseFeatureString(distanceData);
                    }
                    else if (line.StartsWith("IMAGE:"))
                    {
                        currentFeature.ImagePath = line.Substring(6);
                    }
                    else if (line.StartsWith("TIME:"))
                    {
                        DateTime time;
                        if (DateTime.TryParse(line.Substring(5), out time))
                        {
                            currentFeature.CreateTime = time;
                        }
                    }
                }
            }

            if (currentFeature != null)
            {
                featureDatabase.Add(currentFeature);
            }

            WriteLog("成功加载 " + featureDatabase.Count + " 个历史特征值");
        }
        catch (Exception ex)
        {
            WriteLog("加载特征值数据库失败: " + ex.Message);
            featureDatabase = new List<FeatureData>();
        }
    }

    /// <summary>
    /// 保存特征值数据库
    /// </summary>
    private void SaveFeatureDatabase()
    {
        if (!enablePersistence) return;

        try
        {
            // 确保数据库文件目录存在
            string dbDir = Path.GetDirectoryName(databaseFile);
            if (!string.IsNullOrEmpty(dbDir) && !Directory.Exists(dbDir))
            {
                Directory.CreateDirectory(dbDir);
                WriteLog("创建数据库目录: " + dbDir);
            }

            // 如果超过最大数量，删除最旧的记录
            if (featureDatabase.Count > maxFeatureCount)
            {
                featureDatabase = featureDatabase
                    .OrderByDescending(f => f.CreateTime)
                    .Take(maxFeatureCount)
                    .ToList();
            }

            List<string> lines = new List<string>();
            foreach (FeatureData feature in featureDatabase)
            {
                lines.Add("FEATURE:");
                lines.Add("ANGLES:" + FormatFeatureString(feature.Angles));
                lines.Add("DISTANCES:" + FormatFeatureString(feature.Distances));
                lines.Add("IMAGE:" + feature.ImagePath);
                lines.Add("TIME:" + feature.CreateTime.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                lines.Add(""); // Empty line separator
            }

            File.WriteAllLines(databaseFile, lines.ToArray());

            WriteLog("特征值数据库已保存，共 " + featureDatabase.Count + " 条记录");
        }
        catch (Exception ex)
        {
            WriteLog("保存特征值数据库失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 解析特征字符串为数值列表（支持分号和逗号分隔符）
    /// </summary>
    private List<double> ParseFeatureString(string featureStr)
    {
        List<double> result = new List<double>();
        if (string.IsNullOrEmpty(featureStr)) return result;

        // 首先尝试分号分隔符（用于输入参数）
        char delimiter = ';';
        if (!featureStr.Contains(';') && featureStr.Contains(','))
        {
            // 如果没有分号但有逗号，使用逗号分隔符（用于数据库存储）
            delimiter = ',';
        }

        string[] parts = featureStr.Split(delimiter);
        foreach (string part in parts)
        {
            double value;
            if (double.TryParse(part.Trim(), out value))
            {
                result.Add(value);
            }
        }
        return result;
    }

    /// <summary>
    /// 格式化数值列表为字符串
    /// </summary>
    private string FormatFeatureString(List<double> values)
    {
        if (values == null || values.Count == 0) return "";

        List<string> stringValues = new List<string>();
        foreach (double value in values)
        {
            stringValues.Add(value.ToString("F6"));
        }
        return string.Join(",", stringValues.ToArray());
    }



    /// <summary>
    /// 计算两个特征值序列的相似度
    /// </summary>
    /// <param name="angles1">角度序列1</param>
    /// <param name="distances1">距离序列1</param>
    /// <param name="angles2">角度序列2</param>
    /// <param name="distances2">距离序列2</param>
    /// <returns>相似度分数（0-1，1为完全匹配）</returns>
    private double CalculateSimilarity(List<double> angles1, List<double> distances1,
                                     List<double> angles2, List<double> distances2)
    {
        if (angles1.Count != angles2.Count || distances1.Count != distances2.Count)
            return 0.0;

        double angleScore = 0.0;
        double distanceScore = 0.0;

        // 计算角度相似度
        for (int i = 0; i < angles1.Count; i++)
        {
            double angleDiff = Math.Abs(angles1[i] - angles2[i]);
            if (angleDiff <= angleThreshold)
            {
                angleScore += (angleThreshold - angleDiff) / angleThreshold;
            }
        }
        angleScore /= angles1.Count;

        // 计算距离相似度
        for (int i = 0; i < distances1.Count; i++)
        {
            double distanceDiff = Math.Abs(distances1[i] - distances2[i]);
            if (distanceDiff <= distanceThreshold)
            {
                distanceScore += (distanceThreshold - distanceDiff) / distanceThreshold;
            }
        }
        distanceScore /= distances1.Count;

        // 综合相似度（角度和距离各占50%权重）
        return (angleScore + distanceScore) / 2.0;
    }

    /// <summary>
    /// 记录图像信息到日志（替代图像保存功能）
    /// </summary>
    /// <param name="image">VM图像对象</param>
    /// <param name="angles">角度特征值</param>
    /// <param name="distances">距离特征值</param>
    /// <returns>记录标识符</returns>
    private string LogImageInfo(object image, List<double> angles, List<double> distances)
    {
        try
        {
            DateTime now = DateTime.Now;
            string timeStamp = now.ToString("HHmmss_fff");
            string recordId = "Record_" + timeStamp;

            // 记录图像基本信息
            if (image != null)
            {
                var imageType = image.GetType();
                WriteLog("=== 图像记录开始 ===");
                WriteLog("记录ID: " + recordId);
                WriteLog("时间戳: " + now.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                WriteLog("图像对象类型: " + imageType.Name);

                // 尝试获取图像尺寸信息
                try
                {
                    var widthProp = imageType.GetProperty("Width");
                    var heightProp = imageType.GetProperty("Height");
                    if (widthProp != null && heightProp != null)
                    {
                        var width = widthProp.GetValue(image);
                        var height = heightProp.GetValue(image);
                        WriteLog("图像尺寸: " + width + " x " + height);
                    }
                }
                catch
                {
                    WriteLog("无法获取图像尺寸信息");
                }
            }
            else
            {
                WriteLog("=== 图像记录开始 ===");
                WriteLog("记录ID: " + recordId);
                WriteLog("警告: 图像对象为空");
            }

            // 记录特征值信息
            WriteLog("角度特征值数量: " + angles.Count);
            WriteLog("距离特征值数量: " + distances.Count);

            // 记录前5个特征值作为样本
            if (angles.Count > 0)
            {
                int sampleCount = Math.Min(5, angles.Count);
                string angleSample = "";
                for (int i = 0; i < sampleCount; i++)
                {
                    angleSample += angles[i].ToString("F3");
                    if (i < sampleCount - 1) angleSample += ", ";
                }
                WriteLog("角度样本(前" + sampleCount + "个): " + angleSample);
            }

            if (distances.Count > 0)
            {
                int sampleCount = Math.Min(5, distances.Count);
                string distanceSample = "";
                for (int i = 0; i < sampleCount; i++)
                {
                    distanceSample += distances[i].ToString("F3");
                    if (i < sampleCount - 1) distanceSample += ", ";
                }
                WriteLog("距离样本(前" + sampleCount + "个): " + distanceSample);
            }

            WriteLog("=== 图像记录结束 ===");

            return recordId;
        }
        catch (Exception ex)
        {
            string errorMsg = "记录图像信息失败: " + ex.Message;
            WriteLog(errorMsg);
            return null;
        }
    }

    /// <summary>
    /// 查找匹配的特征值
    /// </summary>
    /// <param name="currentAngles">当前角度特征值</param>
    /// <param name="currentDistances">当前距离特征值</param>
    /// <returns>匹配的特征值数据列表</returns>
    private List<FeatureData> FindMatchingFeatures(List<double> currentAngles, List<double> currentDistances)
    {
        List<FeatureData> matches = new List<FeatureData>();

        foreach (var feature in featureDatabase)
        {
            double similarity = CalculateSimilarity(currentAngles, currentDistances,
                                                   feature.Angles, feature.Distances);
            if (similarity >= similarityThreshold)
            {
                feature.LastSimilarity = similarity;
                matches.Add(feature);
            }
        }

        // 按相似度排序
        matches = matches.OrderByDescending(f => f.LastSimilarity).ToList();

        WriteLog("找到 " + matches.Count + " 个匹配的特征值");
        return matches;
    }

    /// <summary>
    /// 设置匹配结果状态码并同步到外部输出变量
    /// </summary>
    private void SetMatchResult(int resultCode)
    {
        matchResult = resultCode;
        out0 = resultCode;  // 同步赋值给外部输出变量
    }

    /// <summary>
    /// 写入匹配结果到每日报告文件
    /// </summary>
    private void WriteMatchResult(int resultCode, string errorDetail = "")
    {
        try
        {
            DateTime now = DateTime.Now;
            string dateStr = now.ToString("yyyy-MM-dd");
            string resultDir = @"D:\HookFC\Reports";

            // 确保报告目录存在
            if (!Directory.Exists(resultDir))
            {
                Directory.CreateDirectory(resultDir);
                WriteLog("创建报告目录: " + resultDir);
            }

            string resultFile = Path.Combine(resultDir, "MatchResult_" + dateStr + ".txt");
            string timeStr = now.ToString("HH:mm:ss.fff");

            // 根据状态码生成结果描述
            string resultDesc = GetResultDescription(resultCode);
            string errorInfo = string.IsNullOrEmpty(errorDetail) ? "" : " | 错误详情: " + errorDetail;

            string resultLine = string.Format("[{0}] 状态码: {1} | 结果: {2}{3}",
                timeStr, resultCode, resultDesc, errorInfo);

            // 追加写入文件
            File.AppendAllText(resultFile, resultLine + Environment.NewLine, Encoding.UTF8);

            WriteLog("匹配结果已记录到: " + resultFile);
        }
        catch (Exception ex)
        {
            WriteLog("写入匹配结果失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 根据状态码获取结果描述
    /// </summary>
    private string GetResultDescription(int resultCode)
    {
        switch (resultCode)
        {
            case 3: return "找到多个匹配（5个或以上）";
            case 2: return "找到少量匹配（2-4个）";
            case 1: return "找到单个匹配";
            case 0: return "处理成功但无匹配";
            case -1: return "输入参数错误";
            case -2: return "特征值解析失败";
            case -3: return "记录失败";
            case -99: return "系统异常错误";
            default: return "未知状态码";
        }
    }

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
        try
        {
            WriteLog("开始处理新的特征值");

            // 获取输入参数
            string dangleStr = dangle;
            string disStr = dis;
            object picImage = pic;

            if (string.IsNullOrEmpty(dangleStr) || string.IsNullOrEmpty(disStr) || picImage == null)
            {
                string errorMsg = "输入参数不完整，请检查dangle、dis和pic参数";
                WriteLog(errorMsg);
                SetMatchResult(-1);  // 输入参数错误
                WriteMatchResult(matchResult, "dangle、dis或pic参数为空或无效");
                return false;
            }

            WriteLog("输入参数 - dangle: " + dangleStr.Length + "个字符, dis: " + disStr.Length + "个字符");

            // 解析特征值
            List<double> currentAngles = ParseFeatureString(dangleStr);
            List<double> currentDistances = ParseFeatureString(disStr);

            if (currentAngles.Count == 0 || currentDistances.Count == 0)
            {
                string errorMsg = "特征值解析失败，请检查输入格式";
                WriteLog(errorMsg);
                SetMatchResult(-2);  // 特征值解析失败
                WriteMatchResult(matchResult, "输入字符串格式错误，无法解析为数值");
                return false;
            }

            WriteLog("解析得到 " + currentAngles.Count + " 个角度值和 " + currentDistances.Count + " 个距离值");

            // 查找匹配的特征值
            List<FeatureData> matches = FindMatchingFeatures(currentAngles, currentDistances);

            // 记录当前图像信息
            string recordId = LogImageInfo(picImage, currentAngles, currentDistances);

            if (!string.IsNullOrEmpty(recordId))
            {
                // 创建新的特征值数据
                FeatureData newFeature = new FeatureData
                {
                    Angles = currentAngles,
                    Distances = currentDistances,
                    ImagePath = recordId  // 使用记录ID代替图像路径
                };

                // 添加到特征值数据库
                featureDatabase.Add(newFeature);
                WriteLog("新特征值已添加到数据库，当前共有 " + featureDatabase.Count + " 条记录");

                // 保存数据库
                SaveFeatureDatabase();

                // 输出匹配结果并设置状态码
                if (matches.Count > 0)
                {
                    string matchInfo = "找到 " + matches.Count + " 个匹配的特征值:\n";
                    int displayCount = Math.Min(matches.Count, maxDisplayMatches);

                    for (int i = 0; i < displayCount; i++)
                    {
                        matchInfo += "匹配 " + (i + 1) + ": 相似度 " + matches[i].LastSimilarity.ToString("F3") + ", " +
                                   "创建时间 " + matches[i].CreateTime.ToString("yyyy-MM-dd HH:mm:ss") + ", " +
                                   "记录ID: " + matches[i].ImagePath + "\n";
                    }

                    if (matches.Count > maxDisplayMatches)
                    {
                        matchInfo += "... 还有 " + (matches.Count - maxDisplayMatches) + " 个匹配结果";
                    }

                    WriteLog("匹配结果: " + matches.Count + " 个匹配项");
                    WriteLog("匹配详情: " + matchInfo);

                    // 根据匹配数量设置状态码
                    if (matches.Count >= 5)
                    {
                        SetMatchResult(3);  // 找到多个匹配（5个或以上）
                        WriteMatchResult(matchResult, "匹配数量: " + matches.Count + "个");
                    }
                    else if (matches.Count >= 2)
                    {
                        SetMatchResult(2);  // 找到少量匹配（2-4个）
                        WriteMatchResult(matchResult, "匹配数量: " + matches.Count + "个");
                    }
                    else
                    {
                        SetMatchResult(1);  // 找到单个匹配
                        WriteMatchResult(matchResult, "匹配数量: " + matches.Count + "个");
                    }
                }
                else
                {
                    string noMatchMsg = "未找到匹配的特征值";
                    WriteLog(noMatchMsg);
                    SetMatchResult(0);  // 处理成功但无匹配
                    WriteMatchResult(matchResult, "相似度阈值: " + similarityThreshold);
                }

                WriteLog("处理完成，记录ID: " + recordId);
            }
            else
            {
                WriteLog("图像信息记录失败");
                SetMatchResult(-3);  // 记录失败（保持原状态码含义）
                WriteMatchResult(matchResult, "图像信息记录过程中出现错误");
            }

            processCount++;
            WriteLog("处理完成，总处理次数: " + processCount);
            return true;
        }
        catch (Exception ex)
        {
            string errorMsg = "处理过程中发生错误: " + ex.Message;
            WriteLog(errorMsg);
            WriteLog("错误堆栈: " + ex.StackTrace);
            SetMatchResult(-99);  // 系统异常错误
            WriteMatchResult(matchResult, ex.Message);
            return false;
        }
    }
}
