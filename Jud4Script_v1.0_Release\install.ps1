# Jud4Script.dll PowerShell 安装脚本
# 需要管理员权限运行

Write-Host "==========================================" -ForegroundColor Green
Write-Host "     Jud4Script.dll 自动安装脚本" -ForegroundColor Green  
Write-Host "==========================================" -ForegroundColor Green
Write-Host ""

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator"))
{
    Write-Host "❌ 需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'，然后执行此脚本" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

Write-Host "✅ 管理员权限检查通过" -ForegroundColor Green
Write-Host ""

# 创建配置目录
Write-Host "1. 创建配置目录..." -ForegroundColor Cyan
$configDir = "D:\LvConfig\产品型号"
if (!(Test-Path $configDir)) {
    New-Item -ItemType Directory -Path $configDir -Force | Out-Null
    Write-Host "✅ 配置目录创建成功: $configDir" -ForegroundColor Green
} else {
    Write-Host "✅ 配置目录已存在: $configDir" -ForegroundColor Green
}
Write-Host ""

# 复制配置文件示例
Write-Host "2. 复制配置文件示例..." -ForegroundColor Cyan
try {
    Copy-Item "config_samples\*.json" $configDir -Force
    Write-Host "✅ 配置文件示例复制成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 配置文件示例复制失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 显示DLL文件位置
Write-Host "3. DLL文件位置信息..." -ForegroundColor Cyan
$currentPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Write-Host "   Jud4Script.dll 和 Newtonsoft.Json.dll 位于: $currentPath\bin\" -ForegroundColor Yellow
Write-Host "   请将这两个文件复制到您的VisionMaster项目目录中" -ForegroundColor Yellow
Write-Host ""

# 运行测试程序
Write-Host "4. 运行安装验证测试..." -ForegroundColor Cyan
Write-Host "正在启动测试程序..." -ForegroundColor Yellow
Write-Host ""

try {
    Set-Location "$currentPath\test"
    Copy-Item "..\bin\*.dll" . -Force
    & ".\TestJud4Script.exe"
    Set-Location $currentPath
} catch {
    Write-Host "❌ 测试程序运行失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

Write-Host "==========================================" -ForegroundColor Green
Write-Host "安装完成！" -ForegroundColor Green
Write-Host ""
Write-Host "下一步操作：" -ForegroundColor Cyan
Write-Host "1. 将 bin\ 目录中的 DLL 文件复制到您的项目目录" -ForegroundColor White
Write-Host "2. 根据需要修改 D:\LvConfig\产品型号\ 中的配置文件" -ForegroundColor White  
Write-Host "3. 在VisionMaster中使用 JudgmentEngine.Evaluate() 方法" -ForegroundColor White
Write-Host ""
Write-Host "详细使用说明请参考 docs\ 目录中的文档" -ForegroundColor Yellow
Write-Host "==========================================" -ForegroundColor Green

Read-Host "按回车键退出"
