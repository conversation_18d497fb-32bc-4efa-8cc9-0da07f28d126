@echo off
chcp 65001 >nul
title 检测项目配置管理器 - 卸载程序

echo ========================================
echo 检测项目配置管理器 v1.2.1 卸载程序
echo ========================================
echo.

:: 获取安装路径
if exist "uninstall_info.bat" (
    call "uninstall_info.bat"
) else (
    echo [警告] 未找到安装信息，将尝试自动检测...
    set "install_path=%~dp0"
)

echo 检测到安装路径: %install_path%
echo.
echo [警告] 此操作将完全删除程序及其所有文件
echo 请确保您已备份重要的配置文件！
echo.
set /p confirm=确认卸载? (Y/N): 
if /i not "%confirm%"=="Y" goto cancel

echo.
echo [信息] 开始卸载...

:: 结束可能正在运行的程序
echo [信息] 检查并结束正在运行的程序...
taskkill /f /im "CfgInspectItems.exe" >nul 2>&1

:: 删除桌面快捷方式
echo [信息] 删除桌面快捷方式...
if exist "%USERPROFILE%\Desktop\检测项目配置管理器.lnk" (
    del /f /q "%USERPROFILE%\Desktop\检测项目配置管理器.lnk" >nul 2>&1
)

:: 删除开始菜单快捷方式
echo [信息] 删除开始菜单快捷方式...
if exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\检测项目配置管理器" (
    rmdir /s /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\检测项目配置管理器" >nul 2>&1
)

:: 询问是否保留用户数据
echo.
echo 是否保留用户配置和数据文件？
echo Y - 保留配置文件（推荐，便于重新安装）
echo N - 完全删除所有文件
echo.
set /p keep_data=请选择 (Y/N): 

if /i "%keep_data%"=="N" (
    echo [信息] 删除用户配置文件...
    :: 删除可能的用户配置文件
    if exist "%APPDATA%\CfgInspectItems" (
        rmdir /s /q "%APPDATA%\CfgInspectItems" >nul 2>&1
    )
    if exist "%LOCALAPPDATA%\CfgInspectItems" (
        rmdir /s /q "%LOCALAPPDATA%\CfgInspectItems" >nul 2>&1
    )
) else (
    echo [信息] 保留用户配置文件
)

:: 删除程序文件
echo [信息] 删除程序文件...
cd /d "%TEMP%"

:: 创建延迟删除脚本
echo @echo off > "%TEMP%\delayed_uninstall.bat"
echo timeout /t 2 /nobreak ^>nul >> "%TEMP%\delayed_uninstall.bat"
echo rmdir /s /q "%install_path%" ^>nul 2^>^&1 >> "%TEMP%\delayed_uninstall.bat"
echo if exist "%install_path%" ^( >> "%TEMP%\delayed_uninstall.bat"
echo     echo [警告] 部分文件可能仍在使用中，请手动删除: %install_path% >> "%TEMP%\delayed_uninstall.bat"
echo     pause >> "%TEMP%\delayed_uninstall.bat"
echo ^) else ^( >> "%TEMP%\delayed_uninstall.bat"
echo     echo [信息] 卸载完成！ >> "%TEMP%\delayed_uninstall.bat"
echo ^) >> "%TEMP%\delayed_uninstall.bat"
echo del /f /q "%TEMP%\delayed_uninstall.bat" ^>nul 2^>^&1 >> "%TEMP%\delayed_uninstall.bat"

echo.
echo ========================================
echo 卸载即将完成
echo ========================================
echo.
echo 程序文件将在2秒后自动删除
echo 感谢您使用检测项目配置管理器！

:: 启动延迟删除脚本并退出
start "" "%TEMP%\delayed_uninstall.bat"
exit /b 0

:cancel
echo.
echo 卸载已取消
pause
exit /b 0
